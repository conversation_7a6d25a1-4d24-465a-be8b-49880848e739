#!/bin/sh
# Refresh icon cache after installation
if [ -x /usr/bin/update-desktop-database ]; then
    /usr/bin/update-desktop-database -q
fi
if [ -x /usr/bin/gtk-update-icon-cache ]; then
    /usr/bin/gtk-update-icon-cache -q /usr/share/icons/hicolor
fi

# Set up Go environment if Go is installed
if command -v go >/dev/null 2>&1; then
    echo "Setting up Go environment for PhantomCannon..."

    # Create GOPATH directories if they don't exist
    mkdir -p /usr/share/phantomcannon/gopath/src
    mkdir -p /usr/share/phantomcannon/gopath/pkg
    mkdir -p /usr/share/phantomcannon/gopath/bin

    # Set permissions
    chmod -R 755 /usr/share/phantomcannon/gopath

    # Create a wrapper script to set GOPATH when running PhantomCannon
    cat > /usr/local/bin/phantomcannon-wrapper << EOF
#!/bin/sh
export GOPATH=/usr/share/phantomcannon/gopath
export GO111MODULE=on
/usr/bin/PhantomCannon "\$@"
EOF

    # Make the wrapper executable
    chmod 755 /usr/local/bin/phantomcannon-wrapper

    # Create a symlink to the wrapper
    ln -sf /usr/local/bin/phantomcannon-wrapper /usr/local/bin/phantomcannon
fi

exit 0
