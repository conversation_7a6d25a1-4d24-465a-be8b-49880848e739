Package: phantomcannon
Version: 1.0.0
Section: utils
Priority: optional
Architecture: amd64
Maintainer: <PERSON> <<EMAIL>>
Depends: libwebkit2gtk-4.1-0, libgtk-3-0, libcurl4, libssl3, libjson-c5, mingw-w64, gcc, make, libcurl4-openssl-dev, libssl-dev, libjson-c-dev, golang
Description: PhantomCannon Command & Control Framework
 PhantomCannon is a command and control framework for security testing.
 It provides a centralized interface for managing implants and
 collecting data from target systems.
