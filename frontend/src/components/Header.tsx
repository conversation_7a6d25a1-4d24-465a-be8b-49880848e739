import { FC, useState } from 'react';
import { cn } from '@/lib/utils';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        Logout: () => Promise<boolean>;
        GetCurrentUser: () => Promise<any>;
      };
    };
  };
  location: any;
};

interface HeaderProps {
  className?: string;
  currentUser?: any;
  onLogout?: () => void;
}

const Header: FC<HeaderProps> = ({ className, currentUser, onLogout }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = async () => {
    if (onLogout) {
      onLogout();
    } else {
      await window.go.main.App.Logout();
      window.location.reload(); // Fallback if no onLogout handler
    }
  };

  return (
    <header className={cn('flex items-center justify-between p-4 border-b border-primary', className)}>
      <div className="flex items-center space-x-4">
        <img src="/image.png" alt="PhantomCannon Logo" className="h-12" />
        <h1 className="text-3xl font-bold neon-text-teal">PhantomCannon</h1>
      </div>
      <div className="flex items-center space-x-4">
        <span className="text-sm text-muted-foreground mr-4">Command & Control</span>

        {currentUser && (
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 cyberpunk-button py-1 px-3"
            >
              <span>{currentUser.username}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M6 9l6 6 6-6" />
              </svg>
            </button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 cyberpunk-panel z-10">
                <div className="py-1">
                  <div className="px-4 py-2 text-sm border-b border-primary border-opacity-30">
                    <div className="font-medium">{currentUser.username}</div>
                    <div className="text-muted-foreground text-xs">{currentUser.role}</div>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="w-full text-left block px-4 py-2 text-sm hover:bg-primary hover:bg-opacity-10"
                  >
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
