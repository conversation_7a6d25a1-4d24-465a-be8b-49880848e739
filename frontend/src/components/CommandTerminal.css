/* Terminal styling */
.terminal-container {
  background-color: rgba(0, 0, 0, 0.9);
  border: 1px solid hsl(var(--primary));
  border-radius: 0.75rem;
  box-shadow: 0 0 10px rgba(0, 206, 209, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.terminal-container:focus-within {
  box-shadow: 0 0 20px rgba(0, 206, 209, 0.5);
}

.terminal-output {
  background-color: rgba(0, 0, 0, 0.8);
  padding: 1rem;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.terminal-entry {
  margin-bottom: 1.5rem;
  animation: fadeIn 0.3s ease-in-out;
}

.terminal-prompt {
  color: hsl(var(--primary));
  margin-right: 0.5rem;
}

.terminal-command {
  color: white;
  font-weight: bold;
}

.terminal-timestamp {
  color: hsl(var(--muted-foreground));
  font-size: 0.75rem;
}

.terminal-output-text {
  margin-top: 0.5rem;
  padding-left: 1rem;
  border-left: 2px solid rgba(0, 206, 209, 0.3);
  white-space: pre-wrap;
  word-break: break-word;
  color: hsl(var(--foreground));
  font-size: 0.75rem;
}

.terminal-input-container {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: rgba(0, 0, 0, 0.95);
  border-top: 1px solid rgba(0, 206, 209, 0.2);
}

.terminal-input {
  background-color: transparent;
  border: none;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  flex-grow: 1;
  outline: none;
}

.terminal-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.copy-button {
  background: none;
  border: none;
  color: hsl(var(--primary));
  cursor: pointer;
  padding: 0.25rem;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.copy-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

.terminal-hint {
  text-align: center;
  color: hsl(var(--muted-foreground));
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

/* Cursor animation */
.terminal-cursor {
  display: inline-block;
  width: 0.5rem;
  height: 1rem;
  background-color: hsl(var(--primary));
  animation: blink 1s step-end infinite;
  vertical-align: middle;
  margin-left: 0.25rem;
}

/* Typing animation */
.typing-animation {
  overflow: hidden;
  border-right: 0.15em solid hsl(var(--primary));
  white-space: nowrap;
  margin: 0;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

/* Loading animation */
.loading-dots {
  display: inline-flex;
  align-items: center;
  height: 1rem;
}

.loading-dots span {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
  margin: 0 2px;
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

/* Animations */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: hsl(var(--primary)); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes loadingDots {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Status indicators */
.status-pending {
  color: hsl(40, 100%, 50%); /* Amber */
}

.status-completed {
  color: hsl(120, 100%, 40%); /* Green */
}

.status-failed {
  color: hsl(0, 100%, 50%); /* Red */
}

/* Scrollbar styling */
.terminal-output::-webkit-scrollbar {
  width: 8px;
}

.terminal-output::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.terminal-output::-webkit-scrollbar-thumb {
  background: rgba(0, 206, 209, 0.3);
  border-radius: 4px;
}

.terminal-output::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 206, 209, 0.5);
}
