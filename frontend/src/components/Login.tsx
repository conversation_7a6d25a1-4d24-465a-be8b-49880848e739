import { FC, useState } from 'react';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        Login: (username: string, password: string) => Promise<boolean>;
      };
    };
  };
  location: any;
};

interface LoginProps {
  onLoginSuccess: () => void;
  onRegisterClick: () => void;
}

const Login: FC<LoginProps> = ({ onLoginSuccess, onRegisterClick }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const success = await window.go.main.App.Login(username, password);
      if (success) {
        onLoginSuccess();
      } else {
        setError('Invalid username or password');
      }
    } catch (err) {
      setError('An error occurred during login');
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="cyberpunk-panel w-full max-w-md p-8">
        <div className="text-center mb-8">
          <img src="/image.png" alt="PhantomCannon Logo" className="h-24 mx-auto mb-4" />
          <h1 className="text-3xl font-bold neon-text-teal">PhantomCannon</h1>
          <p className="text-muted-foreground mt-2">Command & Control</p>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="username" className="block text-sm font-medium mb-1">
              Username
            </label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Enter your username"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Enter your password"
            />
          </div>

          {error && (
            <div className="text-red-500 text-sm p-2 bg-red-500 bg-opacity-10 border border-red-500 border-opacity-20 rounded-md">
              {error}
            </div>
          )}

          <div className="flex flex-col space-y-4">
            <button
              type="submit"
              disabled={loading}
              className="cyberpunk-button w-full"
            >
              {loading ? 'Logging in...' : 'Login'}
            </button>

            <div className="text-center">
              <span className="text-muted-foreground">Don't have an account? </span>
              <button
                type="button"
                onClick={onRegisterClick}
                className="text-primary hover:underline"
              >
                Register
              </button>
            </div>
          </div>
        </form>
      </div>

      <div className="mt-8 text-center text-xs text-muted-foreground">
        <p>Default admin credentials: username: admin, password: admin</p>
        <p>Please change the password after first login</p>
      </div>
    </div>
  );
};

export default Login;
