import { FC, useState } from 'react';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        Register: (username: string, password: string, email: string, fullName: string) => Promise<boolean>;
      };
    };
  };
  location: any;
};

interface RegisterProps {
  onRegisterSuccess: () => void;
  onLoginClick: () => void;
}

const Register: FC<RegisterProps> = ({ onRegisterSuccess, onLoginClick }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    if (!username || !password || !confirmPassword) {
      setError('Please fill in all required fields');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const success = await window.go.main.App.Register(username, password, email, fullName);
      if (success) {
        onRegisterSuccess();
      } else {
        setError('Registration failed. Username may already exist.');
      }
    } catch (err) {
      setError('An error occurred during registration');
      console.error('Registration error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="cyberpunk-panel w-full max-w-md p-8">
        <div className="text-center mb-8">
          <img src="/image.png" alt="PhantomCannon Logo" className="h-24 mx-auto mb-4" />
          <h1 className="text-3xl font-bold neon-text-teal">PhantomCannon</h1>
          <p className="text-muted-foreground mt-2">Create Account</p>
        </div>

        <form onSubmit={handleRegister} className="space-y-4">
          <div>
            <label htmlFor="username" className="block text-sm font-medium mb-1">
              Username <span className="text-red-500">*</span>
            </label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Choose a username"
              required
            />
          </div>

          <div>
            <label htmlFor="fullName" className="block text-sm font-medium mb-1">
              Full Name
            </label>
            <input
              id="fullName"
              type="text"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Enter your full name"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-1">
              Password <span className="text-red-500">*</span>
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Create a password"
              required
            />
            <p className="text-xs text-muted-foreground mt-1">
              Password must be at least 6 characters long
            </p>
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium mb-1">
              Confirm Password <span className="text-red-500">*</span>
            </label>
            <input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="cyberpunk-input w-full"
              placeholder="Confirm your password"
              required
            />
          </div>

          {error && (
            <div className="text-red-500 text-sm p-2 bg-red-500 bg-opacity-10 border border-red-500 border-opacity-20 rounded-md">
              {error}
            </div>
          )}

          <div className="flex flex-col space-y-4 pt-2">
            <button
              type="submit"
              disabled={loading}
              className="cyberpunk-button w-full"
            >
              {loading ? 'Registering...' : 'Register'}
            </button>

            <div className="text-center">
              <span className="text-muted-foreground">Already have an account? </span>
              <button
                type="button"
                onClick={onLoginClick}
                className="text-primary hover:underline"
              >
                Login
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;
