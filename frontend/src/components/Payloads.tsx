import { FC, useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatBytes } from '@/lib/utils';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        BuildLinuxPayload: (serverIP: string, serverPort: string) => Promise<any>;
        BuildLinuxCPayload: (serverIP: string, serverPort: string) => Promise<any>;
        BuildWindowsPayload: (serverIP: string, serverPort: string) => Promise<any>;
        BuildWindowsGoPayload: (serverIP: string, serverPort: string) => Promise<any>;
        GetPayloads: () => Promise<any[]>;
        EnsurePhantomCannonDirectories: () => Promise<boolean>;
        GetC2ServerPort: () => Promise<number>;
      };
    };
  };
};

interface PayloadsProps {}

const Payloads: FC<PayloadsProps> = () => {
  const [activeTab, setActiveTab] = useState('linux');
  const [linuxImplantType, setLinuxImplantType] = useState('go');
  const [windowsImplantType, setWindowsImplantType] = useState('go');
  const [serverIP, setServerIP] = useState('');
  const [serverPort, setServerPort] = useState('');
  const [building, setBuilding] = useState(false);
  const [buildResult, setBuildResult] = useState<any>(null);
  const [payloads, setPayloads] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Load payloads on component mount
  useEffect(() => {
    const loadPayloads = async () => {
      try {
        // Ensure directories exist
        await window.go.main.App.EnsurePhantomCannonDirectories();

        // Get current C2 server port for default value
        const port = await window.go.main.App.GetC2ServerPort();
        setServerPort(port.toString());

        // Get list of payloads
        const payloadsList = await window.go.main.App.GetPayloads();
        setPayloads(payloadsList || []);
      } catch (error) {
        console.error('Failed to load payloads:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPayloads();
  }, []);

  const handleBuildPayload = async () => {
    if (!serverIP || !serverPort) {
      alert('Please enter both server IP and port');
      return;
    }

    setBuilding(true);
    setBuildResult(null);

    try {
      let result;
      if (activeTab === 'linux') {
        if (linuxImplantType === 'go') {
          result = await window.go.main.App.BuildLinuxPayload(serverIP, serverPort);
        } else {
          result = await window.go.main.App.BuildLinuxCPayload(serverIP, serverPort);
        }
      } else {
        if (windowsImplantType === 'go') {
          result = await window.go.main.App.BuildWindowsGoPayload(serverIP, serverPort);
        } else {
          result = await window.go.main.App.BuildWindowsPayload(serverIP, serverPort);
        }
      }

      setBuildResult(result);

      if (result.success) {
        // Refresh the payloads list
        const payloadsList = await window.go.main.App.GetPayloads();
        setPayloads(payloadsList || []);
      }
    } catch (error) {
      console.error('Failed to build payload:', error);
      setBuildResult({
        success: false,
        message: 'An error occurred while building the payload'
      });
    } finally {
      setBuilding(false);
    }
  };

  return (
    <div className="p-6">
      <h2 className="cyberpunk-header text-2xl mb-6">Payload Generator</h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <Card className="cyberpunk-card">
            <CardHeader>
              <CardTitle>Build Payload</CardTitle>
              <CardDescription>
                Generate a payload for your target system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="linux">Linux</TabsTrigger>
                  <TabsTrigger value="windows">Windows</TabsTrigger>
                </TabsList>
                <TabsContent value="linux">
                  <div className="space-y-4">
                    <div>
                      <Label className="block mb-2">Implant Type</Label>
                      <div className="flex space-x-4">
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="linux-go"
                            name="linux-implant-type"
                            value="go"
                            checked={linuxImplantType === 'go'}
                            onChange={() => setLinuxImplantType('go')}
                            className="mr-2"
                          />
                          <label htmlFor="linux-go">Go (Default)</label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="linux-c"
                            name="linux-implant-type"
                            value="c"
                            checked={linuxImplantType === 'c'}
                            onChange={() => setLinuxImplantType('c')}
                            className="mr-2"
                          />
                          <label htmlFor="linux-c">C</label>
                        </div>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="linux-server-ip">Server IP</Label>
                      <Input
                        id="linux-server-ip"
                        placeholder="*************"
                        value={serverIP}
                        onChange={(e) => setServerIP(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="linux-server-port">Server Port</Label>
                      <Input
                        id="linux-server-port"
                        placeholder="5000"
                        value={serverPort}
                        onChange={(e) => setServerPort(e.target.value)}
                      />
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="windows">
                  <div className="space-y-4">
                    <div>
                      <Label className="block mb-2">Implant Type</Label>
                      <div className="flex space-x-4">
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="windows-go"
                            name="windows-implant-type"
                            value="go"
                            checked={windowsImplantType === 'go'}
                            onChange={() => setWindowsImplantType('go')}
                            className="mr-2"
                          />
                          <label htmlFor="windows-go">Go (Recommended)</label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="windows-c"
                            name="windows-implant-type"
                            value="c"
                            checked={windowsImplantType === 'c'}
                            onChange={() => setWindowsImplantType('c')}
                            className="mr-2"
                          />
                          <label htmlFor="windows-c">C</label>
                        </div>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="windows-server-ip">Server IP</Label>
                      <Input
                        id="windows-server-ip"
                        placeholder="*************"
                        value={serverIP}
                        onChange={(e) => setServerIP(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="windows-server-port">Server Port</Label>
                      <Input
                        id="windows-server-port"
                        placeholder="5000"
                        value={serverPort}
                        onChange={(e) => setServerPort(e.target.value)}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter>
              <Button
                className="cyberpunk-button w-full"
                onClick={handleBuildPayload}
                disabled={building}
              >
                {building ? 'Building...' :
                  activeTab === 'linux'
                    ? `Build Linux ${linuxImplantType === 'go' ? 'Go' : 'C'} Payload`
                    : `Build Windows ${windowsImplantType === 'go' ? 'Go' : 'C'} Payload`
                }
              </Button>
            </CardFooter>
          </Card>

          {buildResult && (
            <Alert className={`mt-4 ${buildResult.success ? 'bg-green-900 bg-opacity-20' : 'bg-red-900 bg-opacity-20'}`}>
              <AlertTitle>{buildResult.success ? 'Success' : 'Error'}</AlertTitle>
              <AlertDescription>
                {buildResult.message}
                {buildResult.success && (
                  <div className="mt-2">
                    <p><strong>Payload:</strong> {buildResult.payloadName}</p>
                    <p><strong>Size:</strong> {formatBytes(buildResult.payloadSize)}</p>
                    <p><strong>Path:</strong> {buildResult.payloadPath}</p>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div>
          <Card className="cyberpunk-card">
            <CardHeader>
              <CardTitle>Generated Payloads</CardTitle>
              <CardDescription>
                List of all generated payloads
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-4">Loading payloads...</div>
              ) : payloads.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Size</TableHead>
                        <TableHead>Created</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {payloads.map((payload, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-mono">{payload.name}</TableCell>
                          <TableCell>
                            <span className={`inline-block px-2 py-1 rounded text-xs ${
                              payload.type === 'linux' ? 'bg-blue-900 bg-opacity-30' : 'bg-purple-900 bg-opacity-30'
                            }`}>
                              {payload.type}
                            </span>
                          </TableCell>
                          <TableCell>{formatBytes(payload.size)}</TableCell>
                          <TableCell>{new Date(payload.modTime).toLocaleString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No payloads generated yet
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Payloads;
