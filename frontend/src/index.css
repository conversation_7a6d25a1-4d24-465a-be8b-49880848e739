@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Cyberpunk Theme Colors - Muted Version */
    --background: 0 0% 0%; /* Black background */
    --background-alpha: rgba(0, 0, 0, 0.6); /* More transparent black */
    --foreground: 180 70% 95%; /* Slightly off-white text */

    /* Teal from PhantomCannon logo - Muted */
    --teal-primary: 181 70% 40%; /* Muted teal */
    --teal-glow: 180 80% 45%; /* Muted glow */

    /* Pink/Purple from logo - Muted */
    --pink-primary: 300 70% 45%; /* Muted pink */
    --purple-primary: 270 60% 25%; /* Muted purple */
    --pink-accent: 328 70% 50%; /* Muted pink accent */
    --purple-accent: 275 60% 40%; /* Muted purple accent */

    /* UI Elements */
    --muted: 240 20% 15%;
    --muted-foreground: 180 20% 70%;

    --popover: 240 15% 10%;
    --popover-foreground: 180 70% 90%;

    --card: 240 15% 8%;
    --card-foreground: 180 70% 90%;

    --border: 181 60% 35%;
    --input: 240 15% 20%;

    --primary: 181 70% 40%; /* Muted Teal */
    --primary-foreground: 0 0% 0%;

    --secondary: 300 70% 45%; /* Muted Pink */
    --secondary-foreground: 0 0% 0%;

    --accent: 275 60% 40%; /* Muted Purple */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;

    --ring: 181 60% 35%;

    --radius: 0.75rem; /* Increased radius for more rounded corners */
  }

  /* Always use dark theme */
  .dark {
    --background: 0 0% 0%;
    --foreground: 180 70% 95%;

    --muted: 240 20% 15%;
    --muted-foreground: 180 20% 70%;

    --popover: 240 15% 10%;
    --popover-foreground: 180 70% 90%;

    --card: 240 15% 8%;
    --card-foreground: 180 70% 90%;

    --border: 181 60% 35%;
    --input: 240 15% 20%;

    --primary: 181 70% 40%;
    --primary-foreground: 0 0% 0%;

    --secondary: 300 70% 45%;
    --secondary-foreground: 0 0% 0%;

    --accent: 275 60% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;

    --ring: 181 60% 35%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-color: rgba(0, 0, 0, 0.75); /* More translucent background */
    background-image:
      radial-gradient(circle at 25% 25%, rgba(0, 206, 209, 0.08) 0%, transparent 60%),
      radial-gradient(circle at 75% 75%, rgba(138, 43, 226, 0.08) 0%, transparent 60%);
    min-height: 100vh;
    overflow-x: hidden;
    backdrop-filter: blur(10px); /* Add blur effect for more translucency */
  }
}

/* Cyberpunk UI Elements */
@layer components {
  .cyberpunk-panel {
    @apply bg-black bg-opacity-60 border border-primary rounded-xl p-4 shadow-lg;
    box-shadow: 0 0 10px rgba(0, 206, 209, 0.2);
    backdrop-filter: blur(5px);
  }

  .cyberpunk-header {
    @apply text-primary font-bold text-xl mb-4;
    text-shadow: 0 0 5px rgba(0, 206, 209, 0.5);
  }

  .cyberpunk-button {
    @apply bg-primary bg-opacity-80 text-black font-bold py-2 px-4 rounded-lg transition-all duration-300;
    box-shadow: 0 0 5px rgba(0, 206, 209, 0.5);
  }

  .cyberpunk-button:hover {
    @apply bg-teal-400 bg-opacity-90;
    box-shadow: 0 0 15px rgba(0, 206, 209, 0.7);
    transform: translateY(-1px);
  }

  .cyberpunk-button-secondary {
    @apply bg-secondary bg-opacity-80 text-black font-bold py-2 px-4 rounded-lg transition-all duration-300;
    box-shadow: 0 0 5px rgba(255, 0, 255, 0.5);
  }

  .cyberpunk-button-secondary:hover {
    @apply bg-pink-400 bg-opacity-90;
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.7);
    transform: translateY(-1px);
  }

  .cyberpunk-button-accent {
    @apply bg-accent bg-opacity-80 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300;
    box-shadow: 0 0 5px rgba(138, 43, 226, 0.5);
  }

  .cyberpunk-button-accent:hover {
    @apply bg-purple-500 bg-opacity-90;
    box-shadow: 0 0 15px rgba(138, 43, 226, 0.7);
    transform: translateY(-1px);
  }

  .cyberpunk-input {
    @apply bg-black bg-opacity-60 border border-primary text-foreground p-2 rounded-lg;
    box-shadow: inset 0 0 5px rgba(0, 206, 209, 0.2);
    backdrop-filter: blur(2px);
  }

  .cyberpunk-input:focus {
    @apply outline-none;
    box-shadow: inset 0 0 8px rgba(0, 206, 209, 0.3), 0 0 5px rgba(0, 206, 209, 0.3);
  }

  .cyberpunk-card {
    @apply bg-black bg-opacity-60 border border-primary rounded-xl overflow-hidden transition-all duration-300;
    box-shadow: 0 0 10px rgba(0, 206, 209, 0.15);
    backdrop-filter: blur(5px);
  }

  .cyberpunk-card:hover {
    box-shadow: 0 0 15px rgba(0, 206, 209, 0.3);
    transform: translateY(-2px);
  }

  .cyberpunk-card-header {
    @apply bg-primary bg-opacity-20 p-3 border-b border-primary border-opacity-50 rounded-t-xl;
  }

  .cyberpunk-card-body {
    @apply p-4;
  }

  .cyberpunk-table {
    @apply w-full border-collapse rounded-xl overflow-hidden;
  }

  .cyberpunk-table th {
    @apply bg-primary bg-opacity-20 text-primary p-3 text-left border-b border-primary border-opacity-40;
    text-shadow: 0 0 5px rgba(0, 206, 209, 0.4);
  }

  .cyberpunk-table th:first-child {
    @apply rounded-tl-lg;
  }

  .cyberpunk-table th:last-child {
    @apply rounded-tr-lg;
  }

  .cyberpunk-table td {
    @apply p-3 border-b border-primary border-opacity-20;
  }

  .cyberpunk-table tr:hover {
    @apply bg-primary bg-opacity-5 backdrop-blur-sm;
  }

  .cyberpunk-table tr:last-child td:first-child {
    @apply rounded-bl-lg;
  }

  .cyberpunk-table tr:last-child td:last-child {
    @apply rounded-br-lg;
  }

  .neon-text-teal {
    @apply text-primary;
    text-shadow: 0 0 5px rgba(0, 206, 209, 0.5);
  }

  .neon-text-pink {
    @apply text-secondary;
    text-shadow: 0 0 5px rgba(255, 0, 255, 0.5);
  }

  .neon-text-purple {
    @apply text-accent;
    text-shadow: 0 0 5px rgba(138, 43, 226, 0.5);
  }

  .neon-border {
    @apply border border-primary rounded-xl;
    box-shadow: 0 0 10px rgba(0, 206, 209, 0.2);
    backdrop-filter: blur(5px);
  }

  .neon-border-pink {
    @apply border border-secondary rounded-xl;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.2);
    backdrop-filter: blur(5px);
  }

  .neon-border-purple {
    @apply border border-accent rounded-xl;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.2);
    backdrop-filter: blur(5px);
  }

  .glitch-effect {
    position: relative;
    overflow: hidden;
  }

  .glitch-effect::before,
  .glitch-effect::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.8;
  }

  .glitch-effect::before {
    color: #ff00ff;
    z-index: -1;
    animation: glitch-animation 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both infinite;
    animation-delay: 0.1s;
  }

  .glitch-effect::after {
    color: #00ffff;
    z-index: -2;
    animation: glitch-animation 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) reverse both infinite;
    animation-delay: 0.2s;
  }

  @keyframes glitch-animation {
    0% {
      transform: translate(0);
    }
    20% {
      transform: translate(-2px, 2px);
    }
    40% {
      transform: translate(-2px, -2px);
    }
    60% {
      transform: translate(2px, 2px);
    }
    80% {
      transform: translate(2px, -2px);
    }
    100% {
      transform: translate(0);
    }
  }

  /* Status indicators */
  .status-online {
    @apply inline-block w-3 h-3 rounded-full bg-green-500 bg-opacity-80;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
  }

  .status-offline {
    @apply inline-block w-3 h-3 rounded-full bg-red-500 bg-opacity-80;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-black bg-opacity-30 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary bg-opacity-50 rounded-full;
    backdrop-filter: blur(2px);
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary bg-opacity-70;
  }
}