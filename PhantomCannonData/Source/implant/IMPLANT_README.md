# Nim Implant for Windows

This is a Nim-based implant for Windows that communicates with a C2 server via HTTPS.

## Features

- Secure communication with C2 server using HTTPS
- AES-CFB encryption for data exchange
- XOR encryption for file uploads
- System information gathering
- Process listing
- Command execution
- File upload capability
- Persistence mechanism
- Random sleep intervals to avoid detection

## Requirements

- Nim compiler
- Required Nim packages:
  - puppy
  - winim
  - nimcrypto (for advanced version)

## Building

### Basic Version

```
nim c -d:release --opt:size --passL:-static --passC:-flto --passL:-flto implant.nim
```

Or run `build.bat`.

### Advanced Version (with proper AES-CFB encryption)

```
nim c -d:release --opt:size --passL:-static --passC:-flto --passL:-flto implant_advanced.nim
```

Or run `build_advanced.bat`.

## Configuration

Before building, make sure to update the following constants in the source code:

- `C2_SERVER`: IP address of your C2 server (default: ************)
- `C2_PORT`: Port of your C2 server (default: 5000)
- `MALWARE_KEY`: AES encryption key (must be 16, 24, or 32 bytes)
- `XOR_KEY`: XOR encryption key for file uploads
- `SLEEP_MIN` and `SLEEP_MAX`: Minimum and maximum sleep intervals in seconds
- `PERSISTENCE_NAME`: Registry key name for persistence

## Usage

1. Build the implant
2. Deploy the implant on the target Windows system
3. The implant will automatically establish persistence and connect to the C2 server
4. Control the implant through your C2 server

## Communication Protocol

The implant communicates with the C2 server using the following endpoints:

- `/heartbeat`: Regular check-in to receive commands
- `/info`: Send system information
- `/ps`: Send process list
- `/out`: Send command output
- `/upload`: Upload files

All data is encrypted using AES-CFB encryption and encoded with Base64.

## Disclaimer

This tool is for educational purposes only. Use responsibly and only on systems you have permission to access.
