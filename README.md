# PhantomCannon Project

## About

PhantomCannon is a Command & Control (C2) server with a modern, cyberpunk-inspired UI. It includes a Windows implant written in Nim and Linux implants written in Go and C that communicate with the C2 server via HTTP/HTTPS.

## Directory Structure

- `implant/`: Contains the Windows Nim implant code and related files
  - `implant.nim`: Basic implant with XOR encryption
  - `implant_advanced.nim`: Advanced implant with AES-CFB encryption
  - `test_implant.nim`: Test script to verify implant functionality
  - `build.bat`: Build script for the basic implant
  - `build_advanced.bat`: Build script for the advanced implant
  - `IMPLANT_README.md`: Detailed documentation for the implant
- `linux_nibble/`: Contains the Linux Go implant code
  - `main.go`: Linux implant with AES-CFB encryption
  - `crypto.go`: Encryption utilities for the Linux implant
  - `persistence.go`: Persistence mechanisms for Linux
  - `system.go`: System information gathering utilities
- `linux_implant_c/`: Contains the Linux C implant code
  - `main.c`: Main implant logic
  - `crypto.c`: Encryption/decryption functions
  - `network.c`: HTTP communication
  - `system.c`: System information gathering
  - `implant.h`: Main header file
  - `Makefile`: For building the implant
- `backend/`: Contains the Go backend code for the C2 server
  - `database.go`: SQLite database implementation
  - `c2server.go`: C2 server implementation
  - `auth.go`: Authentication and user management
  - `network.go`: Network interface detection and management
- `frontend/`: Contains the React frontend code
  - Modern UI with cyberpunk theme
  - User authentication and management
  - Implant monitoring and control
  - Payload generation interface
- `Payloads/`: Directory for storing generated payloads

## Features

### C2 Server

- Modern, cyberpunk-inspired UI with translucent effects
- User authentication and role-based access control
- User management for administrators
- Secure communication with implants via HTTP/HTTPS
- Command execution and file upload capabilities
- Detailed logging and monitoring
- Configurable server IP and port
- Network interface selection for multi-homed systems
- Integrated payload generation for Windows and Linux targets
- Remote access API for controlling bots from separate servers

### Windows Implant

- Secure communication with C2 server via HTTPS
- Encryption for data exchange (XOR or AES-CFB)
- System information gathering
- Process listing
- Command execution
- File upload capability
- Persistence mechanism
- Stealth features with random sleep intervals

### Linux Implants

#### Go Implant
- Communication with C2 server via HTTP
- AES-CFB encryption for secure data exchange
- Detailed system information gathering
- Process listing and management
- Command execution with output capture
- Configurable sleep intervals
- Comprehensive logging for debugging
- Cross-platform compatibility

#### C Implant
- Smaller binary size compared to Go implant
- Lower memory footprint
- AES-CFB encryption for secure data exchange
- Uses libcurl for HTTP communication
- System information gathering
- Process listing
- Command execution with output capture
- Detailed logging for debugging
- Requires libcurl, openssl, and json-c dependencies

## Building and Running

### Building the C2 Server

```bash
# For standard build
wails build

# For systems with WebKit2 version 4.1 or higher (recommended for Linux)
wails build -tags webkit2_41
```

The compiled executable will be in the `build/bin` directory.

### Building the Windows Implant Manually

Navigate to the `implant` directory and run one of the build scripts:

```bash
cd implant
build.bat         # For the basic implant
build_advanced.bat # For the advanced implant with AES-CFB encryption
```

### Building the Linux Implants Manually

#### Go Implant

Navigate to the `linux_nibble` directory and use Go to build the implant:

```bash
cd linux_nibble
go build -o linux_implant
```

#### C Implant

Navigate to the `linux_implant_c` directory and use the Makefile to build the implant:

```bash
cd linux_implant_c
# Install dependencies (Debian/Ubuntu)
sudo apt-get install libcurl4-openssl-dev libssl-dev libjson-c-dev
# Build the implant
make
```

### Using the Integrated Payload Builder

PhantomCannon includes an integrated payload builder in the UI:

1. Start the PhantomCannon application
2. Go to the Payloads tab
3. Select the target platform (Windows or Linux)
4. For Linux, select the implant type (Go or C)
5. Enter the C2 server IP and port
6. Click "Build Payload"
7. The generated payload will be saved to the `Payloads` directory

## Usage

### C2 Server

1. Run the PhantomCannon executable
2. Log in with the default admin credentials:
   - Username: `admin`
   - Password: `admin`
3. It's recommended to change the default password after first login
4. Configure the server settings in the Settings tab:
   - Select the network interface to bind to
   - Configure the server port (default is 5000)
   - Start the C2 server to begin listening for implant connections

### Generating and Deploying Payloads

1. Go to the Payloads tab in the PhantomCannon UI
2. Select the target platform (Windows or Linux)
3. For Linux, select the implant type (Go or C)
4. Enter the C2 server IP and port
5. Click "Build Payload"
6. The generated payload will be saved to the `Payloads` directory
7. Transfer the payload to the target system and execute it

### Managing Bots

1. Go to the Bots tab in the PhantomCannon UI
2. View all connected bots with their system information
3. Select a bot to interact with it:
   - Send commands and view their output
   - View system information
   - View process list
   - Upload files

### Remote Access API

1. Go to the Settings tab in the TailGunner UI
2. Configure the Remote Access API Server:
   - Select the network interface to bind to from the dropdown
   - You can choose a different network interface than the one used by the C2 server
   - For example, use your internal network interface for the C2 server and your external/public interface for the API server
   - Set the port (must be different from the C2 server port)
   - Start the API server
   - Note the API key for authentication
3. Remote clients can now connect to the API server using the provided API key
4. API endpoints:
   - `GET /api/bots` - List all connected bots
   - `GET /api/bot/{id}` - Get information about a specific bot
   - `POST /api/command` - Send a command to a bot
   - `GET /api/commands?bot_id={id}` - Get command history for a bot
   - `GET /api/status` - Get server status information
5. All API requests must include the `X-API-Key` header with the API key

#### Network Segmentation Example

- C2 Server: Bind to internal network interface (e.g., 192.168.1.10:5000)
  - Implants connect to this interface
  - Keeps C2 traffic isolated to your internal network
- API Server: Bind to external/public network interface (e.g., 203.0.113.10:8080)
  - Remote operators connect to this interface
  - Allows secure remote access without exposing the C2 server directly

### Windows Implant

See `implant/IMPLANT_README.md` for detailed usage instructions.

### Linux Implants

#### Go Implant

1. Execute the Go Linux implant on the target system:
   ```bash
   chmod +x linux_implant_*
   ./linux_implant_*
   ```
2. The implant will connect to the C2 server and appear in the Bots tab
3. Logs are written to `/tmp/implant.log` for debugging

#### C Implant

1. Make sure the required dependencies are installed on the target system:
   ```bash
   # Debian/Ubuntu
   sudo apt-get install libcurl4 libssl3 libjson-c5
   # Red Hat/CentOS
   sudo yum install libcurl openssl json-c
   ```
2. Execute the C Linux implant on the target system:
   ```bash
   chmod +x linux_implant_c_*
   ./linux_implant_c_*
   ```
3. The implant will connect to the C2 server and appear in the Bots tab
4. Logs are written to `/tmp/implant.log` for debugging

## Security Features

- Secure password hashing with bcrypt
- Session-based authentication
- Role-based access control (admin and user roles)
- Audit logging for security events
- AES-CFB encryption for secure data exchange
- Configurable encryption keys
- Secure cookie-based session management
- Encrypted command and response transmission

## UI Features

- Cyberpunk-inspired design with teal, pink, and purple accents
- Translucent panels with glass-like effects
- Modern rounded corners and subtle animations
- Dark theme optimized for low-light environments
- Responsive layout for different screen sizes
- Tabbed interface for easy navigation
- Real-time bot status updates
- Command history and output display

## Troubleshooting

### C2 Server

- If the server fails to start, check if the port is already in use
- Ensure you have the correct permissions to bind to the selected port
- For port numbers below 1024, you may need to run as administrator/root
- Check the console output for detailed error messages

### Windows Implant

- Ensure Nim is installed and properly configured
- Check that the C2 server IP and port are correctly specified
- Verify that the target system can reach the C2 server
- Some antivirus software may detect and block the implant

### Linux Implants

#### Go Implant
- Ensure Go is installed and properly configured
- Make the implant executable with `chmod +x`
- Check the logs at `/tmp/implant.log` for detailed error messages
- Verify network connectivity to the C2 server
- If the implant doesn't connect, try running with sudo for additional privileges

#### C Implant
- Ensure the required dependencies are installed (libcurl, openssl, json-c)
- Make the implant executable with `chmod +x`
- Check the logs at `/tmp/implant.log` for detailed error messages
- Verify network connectivity to the C2 server
- If you see "Error initializing curl" or similar, check that libcurl is properly installed
- If you see encryption errors, check that openssl is properly installed
- If you see JSON parsing errors, check that json-c is properly installed

### Remote Access API

- If the API server fails to start, check if the port is already in use
- Ensure the API server port is different from the C2 server port
- Verify that remote clients can reach the API server (check firewall settings)
- All API requests must include the `X-API-Key` header with the correct API key
- If you're getting authentication errors, regenerate the API key in the Settings tab
- For network interface issues:
  - Make sure the selected network interface is active and properly configured
  - If binding to a specific interface fails, try using 0.0.0.0 (all interfaces) instead
  - For multi-homed systems, ensure the correct routing is in place
  - Use `ip route` to verify your routing table if remote clients can't connect
  - Consider using port forwarding if your server is behind NAT

## Disclaimer

This tool is for educational purposes only. Use responsibly and only on systems you have permission to access. The authors are not responsible for any misuse or damage caused by this software.
