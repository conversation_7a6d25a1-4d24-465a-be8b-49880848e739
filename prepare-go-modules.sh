#!/bin/bash

# This script pre-downloads Go modules needed for building implants
# and stores them in the GOPATH cache

echo "Preparing Go modules for PhantomCannon..."

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# Create a basic Go module
cat > go.mod << EOF
module phantomcannon/implant

go 1.18

require (
    github.com/gorilla/websocket v1.5.0
    golang.org/x/crypto v0.0.0-20220622213112-05595931fe9d
    golang.org/x/sys v0.0.0-20220708085239-5a0f0661e09d
)
EOF

# Create a dummy Go file
cat > main.go << EOF
package main

import (
    "fmt"
    "github.com/gorilla/websocket"
    "golang.org/x/crypto/chacha20poly1305"
    "golang.org/x/sys/windows"
)

func main() {
    fmt.Println("Dummy file for downloading dependencies")
}
EOF

# Download the dependencies
echo "Downloading Go dependencies..."
go mod download -x

# Clean up
cd -
rm -rf "$TEMP_DIR"

echo "Go modules prepared successfully!"
