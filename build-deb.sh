#!/bin/bash

# Build the Wails application
echo "Building PhantomCannon application..."
wails build -tags webkit2_41

# Prepare Go modules for offline use
echo "Preparing Go modules for offline use..."
./prepare-go-modules.sh

# Create package directories
echo "Creating package directory structure..."
mkdir -p debian-package/DEBIAN
mkdir -p debian-package/usr/bin
mkdir -p debian-package/usr/share/applications
mkdir -p debian-package/usr/share/icons/hicolor/256x256/apps

# Create control file
echo "Creating control file..."
cat > debian-package/DEBIAN/control << EOF
Package: phantomcannon
Version: 1.0.0
Section: utils
Priority: optional
Architecture: amd64
Maintainer: <PERSON> <<EMAIL>>
Depends: libwebkit2gtk-4.1-0, libgtk-3-0, libcurl4, libssl3, libjson-c5, mingw-w64, gcc, make, libcurl4-openssl-dev, libssl-dev, libjson-c-dev, golang
Description: PhantomCannon Command & Control Framework
 PhantomCannon is a command and control framework for security testing.
 It provides a centralized interface for managing implants and
 collecting data from target systems.
EOF

# Create postinst script
echo "Creating postinst script..."
cat > debian-package/DEBIAN/postinst << EOF
#!/bin/sh
# Refresh icon cache after installation
if [ -x /usr/bin/update-desktop-database ]; then
    /usr/bin/update-desktop-database -q
fi
if [ -x /usr/bin/gtk-update-icon-cache ]; then
    /usr/bin/gtk-update-icon-cache -q /usr/share/icons/hicolor
fi
exit 0
EOF
chmod 755 debian-package/DEBIAN/postinst

# Create desktop entry
echo "Creating desktop entry..."
cat > debian-package/usr/share/applications/phantomcannon.desktop << EOF
[Desktop Entry]
Type=Application
Name=PhantomCannon
Comment=PhantomCannon Command & Control Framework
Exec=/usr/local/bin/phantomcannon
Icon=phantomcannon
Terminal=false
Categories=Development;Security;
StartupWMClass=PhantomCannon
Keywords=security;c2;command;control;
EOF

# Copy icon
echo "Copying icon..."
cp build/appicon.png debian-package/usr/share/icons/hicolor/256x256/apps/phantomcannon.png

# Copy binary
echo "Copying application binary..."
cp build/bin/PhantomCannon debian-package/usr/bin/

# Build the package
echo "Building Debian package..."
dpkg-deb --build debian-package phantomcannon_1.0.0_amd64.deb

echo "Package created: phantomcannon_1.0.0_amd64.deb"
