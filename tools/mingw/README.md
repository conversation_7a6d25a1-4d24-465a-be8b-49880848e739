# MinGW for PhantomCannon

This directory is intended to contain a minimal MinGW distribution for building the Windows C implant.

## Installation

1. Download MinGW-W64 from https://www.mingw-w64.org/ or https://winlibs.com/
2. Extract the downloaded archive
3. Copy the following files to the `bin` directory:
   - gcc.exe
   - as.exe
   - ld.exe
   - collect2.exe
   - libwinhttp.a
   - libcrypt32.a
   - libadvapi32.a
   - And any other required DLLs and libraries

Alternatively, you can install MinGW system-wide and ensure it's in your PATH.

## Usage

TailGunner will automatically detect and use the MinGW installation in this directory when building the Windows C implant. If not found, it will fall back to using the system-installed MinGW.

## Minimal Required Files

At a minimum, you need:

- gcc.exe and its dependencies
- The Windows libraries (libwinhttp.a, libcrypt32.a, libadvapi32.a)
- Required header files

A full MinGW distribution is recommended for simplicity.
