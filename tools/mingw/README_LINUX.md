# Cross-Compiling Windows Payloads on Linux

This guide explains how to set up your Linux system to compile Windows payloads for PhantomCannon.

## Prerequisites

You need to install the MinGW-w64 cross-compiler on your Linux system:

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install mingw-w64
```

### Fedora
```bash
sudo dnf install mingw64-gcc
```

### Arch Linux
```bash
sudo pacman -S mingw-w64-gcc
```

## Setup

1. After installing the MinGW-w64 cross-compiler, run the setup script:

```bash
cd tools/mingw
chmod +x setup_linux.sh
./setup_linux.sh
```

This script will create symlinks in the `tools/mingw/bin` directory pointing to your system's MinGW cross-compiler.

2. Verify the setup:

```bash
ls -la tools/mingw/bin
```

You should see symlinks to `x86_64-w64-mingw32-gcc` and other MinGW tools.

## How It Works

When you click "Create" in the Payloads tab for a Windows payload, PhantomCannon will:

1. Look for the cross-compiler in the `tools/mingw/bin` directory
2. If found, use it to compile the Windows C implant
3. If not found, try to use the system's cross-compiler
4. Use the MinGW libraries in `tools/mingw/x86_64-w64-mingw32/lib` for linking

## Troubleshooting

If you encounter issues:

1. Make sure the MinGW-w64 cross-compiler is installed correctly:
   ```bash
   which x86_64-w64-mingw32-gcc
   ```

2. Check if the symlinks were created properly:
   ```bash
   ls -la tools/mingw/bin
   ```

3. Run the setup script again:
   ```bash
   ./tools/mingw/setup_linux.sh
   ```

4. Check the PhantomCannon logs for compilation errors.
