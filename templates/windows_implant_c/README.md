# C Implant for Windows

This is a C-based implant for Windows that communicates with the PhantomCannon C2 server via HTTPS.

## Features

- Secure communication with C2 server using HTTPS
- AES-CFB encryption for data exchange
- XOR encryption for file uploads
- System information gathering
- Process listing
- Command execution
- File upload capability
- Multiple persistence mechanisms
- Random sleep intervals to avoid detection

## Requirements

- MinGW or other C compiler for Windows
- Windows API headers
- WinHTTP library

## Building

### Using the build script

```batch
build.bat
```

### Using make

```batch
make
```

## Configuration

The following constants are automatically configured during the build process:

- `C2_SERVER`: IP address of your C2 server
- `C2_PORT`: Port of your C2 server
- `MALWARE_KEY`: AES encryption key
- `XOR_KEY`: XOR encryption key for file uploads
- `SLEEP_MIN` and `SLEEP_MAX`: Minimum and maximum sleep intervals in seconds
- `PERSISTENCE_NAME`: Name for persistence registry keys and scheduled tasks

## Communication Protocol

The implant communicates with the C2 server using the following endpoints:

- `/heartbeat`: Regular check-in to receive commands
- `/info`: Send system information
- `/ps`: Send process list
- `/out`: Send command output
- `/upload`: Upload files

All data is encrypted using AES-CFB encryption and encoded with Base64.

## Persistence Mechanisms

The implant attempts to establish persistence using multiple methods:

1. Registry Run key
2. Scheduled task
3. Startup folder

## Disclaimer

This tool is for educational purposes only. Use responsibly and only on systems you have permission to access.
