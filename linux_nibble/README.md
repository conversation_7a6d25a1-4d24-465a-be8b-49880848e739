# Go Implant for Linux

This is a Go-based implant for Linux that communicates with the PhantomCannon C2 server via HTTPS.

## Features

- Secure communication with C2 server using HTTPS
- AES-CFB encryption for data exchange
- XOR encryption for file uploads
- System information gathering
- Process listing
- Command execution
- File upload capability
- Multiple persistence mechanisms
- Random sleep intervals to avoid detection

## Requirements

- Go compiler
- Linux target system

## Building

To build the implant, run:

```bash
go build -o linux_implant
```

For a smaller binary size, you can use:

```bash
go build -ldflags="-s -w" -o linux_implant
```

## Configuration

Before building, make sure to update the following constants in the source code:

- `C2_SERVER`: IP address of your C2 server (default: ************)
- `C2_PORT`: Port of your C2 server (default: 5000)
- `MALWARE_KEY`: AES encryption key (must be 32 bytes for AES-256)
- `XOR_KEY`: XOR encryption key for file uploads
- `SLEEP_MIN` and `SLEEP_MAX`: Minimum and maximum sleep intervals in seconds
- `PERSISTENCE_NAME`: Name for persistence service/scripts

## Usage

1. Build the implant
2. Deploy the implant on the target Linux system
3. The implant will automatically establish persistence and connect to the C2 server
4. Control the implant through your C2 server

## Communication Protocol

The implant communicates with the C2 server using the following endpoints:

- `/heartbeat`: Regular check-in to receive commands
- `/info`: Send system information
- `/ps`: Send process list
- `/out`: Send command output
- `/upload`: Upload files

All data is encrypted using AES-CFB encryption and encoded with Base64.

## Persistence Mechanisms

The implant attempts to establish persistence using multiple methods:

1. Systemd service
2. Cron job
3. Entries in .bashrc and .profile

## Disclaimer

This tool is for educational purposes only. Use responsibly and only on systems you have permission to access.
