package main

import (
	"log"
	"os"
	"path/filepath"
	"strings"
)

// init is called automatically before main()
func init() {
	// Set up logging
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("PhantomCannon initializing...")

	// Get the current working directory
	cwd, err := os.Getwd()
	if err != nil {
		log.Printf("WARNING: Failed to get current working directory: %v", err)
		return
	}

	// Check if we're running from /tmp
	if strings.HasPrefix(cwd, "/tmp") {
		log.Printf("WARNING: Running from /tmp directory: %s", cwd)
	}

	// Always use PhantomCannonData directory in the current working directory
	phantomcannonDir := filepath.Join(cwd, "PhantomCannonData")

	// Create the directory if it doesn't exist
	if _, err := os.Stat(phantomcannonDir); os.IsNotExist(err) {
		log.Printf("Creating PhantomCannonData directory: %s", phantomcannonDir)
		if err := os.MkdirAll(phantomcannonDir, 0755); err != nil {
			log.Printf("WARNING: Failed to create PhantomCannonData directory: %v", err)
		}
	}

	// Check if /tmp/TailGunner or /tmp/TailGunnerData exists and is a directory
	tmpTailgunnerDir := "/tmp/TailGunner"
	if fileInfo, err := os.Stat(tmpTailgunnerDir); err == nil && fileInfo.IsDir() {
		log.Printf("WARNING: Found /tmp/TailGunner directory, this should not be used")
	}

	tmpTailgunnerDataDir := "/tmp/TailGunnerData"
	if fileInfo, err := os.Stat(tmpTailgunnerDataDir); err == nil && fileInfo.IsDir() {
		log.Printf("WARNING: Found /tmp/TailGunnerData directory, this should not be used")
	}

	// Check if TailGunner directory exists (old directory name)
	oldTailgunnerDir := filepath.Join(cwd, "TailGunner")
	if fileInfo, err := os.Stat(oldTailgunnerDir); err == nil && fileInfo.IsDir() {
		log.Printf("Found old TailGunner directory, attempting to migrate files to PhantomCannonData")
		migrateOldDirectory(oldTailgunnerDir, phantomcannonDir)
	}
}
